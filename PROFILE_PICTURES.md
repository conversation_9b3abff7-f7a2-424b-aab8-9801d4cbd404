# Profile Pictures Implementation

This document explains the profile picture system implemented in the chat application.

## Overview

Profile pictures are now served through a custom R2 wrapper API that allows public access without authorization. This solves the issue where profile pictures couldn't be displayed properly due to authorization requirements.

## Architecture

### Database Storage
- **Old System**: Stored full R2 URLs in `User.profilePicture` field
- **New System**: Stores R2 keys in `User.profilePictureKey` field, with a virtual `profilePicture` field that generates public URLs

### API Endpoints

#### Public Avatar Endpoint
- **URL**: `/api/public/avatar/[key]`
- **Method**: GET
- **Authorization**: None required
- **Purpose**: Serves profile pictures publicly using R2 keys
- **Security**: Validates that the key belongs to a user's profile picture

#### Profile Update Endpoint
- **URL**: `/api/user/profile`
- **Method**: PUT
- **Authorization**: Required
- **Changes**: Now stores R2 keys instead of full URLs

### Frontend Integration

#### Chat Messages
- Messages now include `senderProfilePicture` field
- Profile pictures are displayed next to messages
- Fallback to initials if no profile picture exists

#### User Interface
- Profile pictures appear in:
  - Chat message bubbles
  - User sidebar
  - Settings page

## Implementation Details

### User Model Changes
```typescript
interface IUser extends Document {
    username: string;
    password: string;
    profilePicture?: string; // Virtual field - computed from profilePictureKey
    profilePictureKey?: string; // Actual R2 key stored in database
}

// Virtual field to generate public profile picture URL
UserSchema.virtual('profilePicture').get(function() {
    if (!this.profilePictureKey) {
        return undefined;
    }
    return `/api/public/avatar/${this.profilePictureKey}`;
});
```

### Message Format
```typescript
interface ChatMessage {
    _id: string;
    sender: string;
    senderProfilePicture?: string; // Public URL to profile picture
    message: string;
    timestamp: Date;
    attachments?: IAttachment[];
}
```

### Security Considerations

1. **Key Validation**: The public endpoint validates that the requested key belongs to a user's profile picture
2. **No Direct R2 Access**: All access goes through our API, maintaining control
3. **Public Caching**: Profile pictures are cached for 24 hours to improve performance
4. **CORS Enabled**: Cross-origin requests are allowed for profile pictures

## Migration

### Automatic Migration
A migration script is provided to convert existing profile picture URLs to keys:

```bash
npm run migrate:profile-pictures
```

### Manual Migration
For development or testing, you can run the migration script directly:

```typescript
import migrateProfilePictures from './src/scripts/migrate-profile-pictures';
await migrateProfilePictures();
```

## Benefits

1. **No Authorization Required**: Profile pictures load without authentication
2. **Better Performance**: Public caching improves load times
3. **Cleaner URLs**: Consistent URL format across the application
4. **Security**: Controlled access through our API wrapper
5. **Scalability**: Can easily add features like image resizing or optimization

## Usage Examples

### Uploading a Profile Picture
```typescript
const formData = new FormData();
formData.append('profilePicture', file);

const response = await fetch('/api/user/profile', {
    method: 'PUT',
    credentials: 'include',
    body: formData
});
```

### Displaying Profile Pictures
```svelte
{#if user.profilePicture}
    <img src={user.profilePicture} alt={user.username} />
{:else}
    <div class="avatar-placeholder">{user.username[0]}</div>
{/if}
```

### Chat Message with Profile Picture
```svelte
{#if msg.senderProfilePicture}
    <img src={msg.senderProfilePicture} alt={msg.sender} />
{:else}
    <div class="avatar">{msg.sender[0]}</div>
{/if}
```

## Troubleshooting

### Profile Pictures Not Loading
1. Check if R2 is properly configured
2. Verify the user has a `profilePictureKey` in the database
3. Check browser network tab for 404 errors on avatar endpoint

### Migration Issues
1. Run the migration script: `npm run migrate:profile-pictures`
2. Check database for users with old `profilePicture` URLs
3. Manually update problematic entries

### Performance Issues
1. Profile pictures are cached for 24 hours
2. Consider implementing image optimization
3. Monitor R2 bandwidth usage
