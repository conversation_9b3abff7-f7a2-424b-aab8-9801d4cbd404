<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { fade, scale } from 'svelte/transition';

	export let isOpen = false;
	export let title = 'Confirm Action';
	export let message = 'Are you sure you want to proceed?';
	export let confirmText = 'Confirm';
	export let cancelText = 'Cancel';
	export let confirmVariant: 'danger' | 'primary' = 'primary';
	export let loading = false;

	const dispatch = createEventDispatcher<{
		confirm: void;
		cancel: void;
	}>();

	function handleConfirm() {
		if (!loading) {
			dispatch('confirm');
		}
	}

	function handleCancel() {
		if (!loading) {
			dispatch('cancel');
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleCancel();
		} else if (event.key === 'Enter') {
			handleConfirm();
		}
	}
</script>

{#if isOpen}
	<!-- Backdrop -->
	<div
		class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
		transition:fade={{ duration: 200 }}
		on:click={handleCancel}
		on:keydown={handleKeydown}
		role="dialog"
		aria-modal="true"
		aria-labelledby="dialog-title"
		tabindex="-1"
	>
		<!-- Dialog -->
		<div
			class="w-full max-w-md rounded-lg bg-white p-6 shadow-xl"
			transition:scale={{ duration: 200, start: 0.95 }}
			on:click|stopPropagation
			role="document"
		>
			<!-- Title -->
			<h3 id="dialog-title" class="mb-4 font-mono text-lg font-semibold text-gray-900">
				{title}
			</h3>

			<!-- Message -->
			<p class="mb-6 text-sm text-gray-600">
				{message}
			</p>

			<!-- Actions -->
			<div class="flex gap-3 justify-end">
				<button
					type="button"
					class="rounded-md border border-gray-300 bg-white px-4 py-2 font-mono text-sm text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
					on:click={handleCancel}
					disabled={loading}
				>
					{cancelText}
				</button>
				<button
					type="button"
					class="rounded-md px-4 py-2 font-mono text-sm text-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 {confirmVariant === 'danger'
						? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
						: 'bg-black hover:bg-gray-800 focus:ring-gray-500'}"
					on:click={handleConfirm}
					disabled={loading}
				>
					{#if loading}
						<div class="flex items-center gap-2">
							<div class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
							Loading...
						</div>
					{:else}
						{confirmText}
					{/if}
				</button>
			</div>
		</div>
	</div>
{/if}
