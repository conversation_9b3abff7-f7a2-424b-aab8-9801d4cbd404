<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { websocketService } from '$lib/services/websocket';

	interface Friend {
		_id: string;
		username: string;
		profilePicture?: string;
		friendshipId: string;
	}

	interface FriendRequest {
		_id: string;
		requester?: Friend;
		recipient?: Friend;
		createdAt: string;
	}

	export let onClose: () => void;

	let friends: Friend[] = [];
	let pendingRequests: FriendRequest[] = [];
	let sentRequests: FriendRequest[] = [];
	let newFriendUsername = '';
	let loading = false;
	let error = '';
	let success = '';
	let showConfirmDialog = false;
	let confirmDialogData: { friendshipId: string; username: string } | null = null;

	// Real-time event handlers
	let unsubscribeFriendRequest: (() => void) | null = null;
	let unsubscribeFriendResponse: (() => void) | null = null;
	let unsubscribeFriendshipEstablished: (() => void) | null = null;

	onMount(() => {
		fetchFriends();
		setupRealtimeListeners();
	});

	onDestroy(() => {
		// Clean up real-time listeners
		if (unsubscribeFriendRequest) unsubscribeFriendRequest();
		if (unsubscribeFriendResponse) unsubscribeFriendResponse();
		if (unsubscribeFriendshipEstablished) unsubscribeFriendshipEstablished();
	});

	function setupRealtimeListeners() {
		// Listen for incoming friend requests
		unsubscribeFriendRequest = websocketService.on('friend_request_received', (data) => {
			console.log('Received friend request:', data);
			pendingRequests = [...pendingRequests, data.friendRequest];
			success = `New friend request from ${data.friendRequest.requester.username}`;
			setTimeout(() => success = '', 3000);
		});

		// Listen for friend request responses
		unsubscribeFriendResponse = websocketService.on('friend_request_response', (data) => {
			console.log('Friend request response:', data);
			// Remove from sent requests
			sentRequests = sentRequests.filter(req => req._id !== data.friendRequest._id);

			if (data.action === 'accept') {
				success = `${data.friendRequest.recipient.username} accepted your friend request!`;
			} else {
				success = `${data.friendRequest.recipient.username} declined your friend request.`;
			}
			setTimeout(() => success = '', 3000);
		});

		// Listen for new friendships
		unsubscribeFriendshipEstablished = websocketService.on('friendship_established', (data) => {
			console.log('Friendship established:', data);
			// Add to friends list if not already there
			if (!friends.find(f => f._id === data.friend._id)) {
				friends = [...friends, data.friend];
			}
		});
	}

	async function fetchFriends() {
		try {
			const response = await fetch('/api/friends', {
				credentials: 'include'
			});

			if (response.ok) {
				const data = await response.json();
				friends = data.friends || [];
				pendingRequests = data.pendingRequests || [];
				sentRequests = data.sentRequests || [];
			} else {
				error = 'Failed to fetch friends';
			}
		} catch (err) {
			console.error('Error fetching friends:', err);
			error = 'Failed to fetch friends';
		}
	}

	async function sendFriendRequest() {
		if (!newFriendUsername.trim()) {
			error = 'Please enter a username';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/friends', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ username: newFriendUsername.trim() })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				newFriendUsername = '';
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to send friend request';
			}
		} catch (err) {
			console.error('Error sending friend request:', err);
			error = 'Failed to send friend request';
		} finally {
			loading = false;
		}
	}

	async function manageFriendRequest(requestId: string, action: 'accept' | 'decline') {
		try {
			const response = await fetch(`/api/friends/${requestId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ action })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				// Remove from pending requests
				pendingRequests = pendingRequests.filter(req => req._id !== requestId);

				// If accepted, refresh friends list
				if (action === 'accept') {
					await fetchFriends();
				}
			} else {
				error = data.error || `Failed to ${action} friend request`;
			}
		} catch (err) {
			console.error(`Error ${action}ing friend request:`, err);
			error = `Failed to ${action} friend request`;
		}
	}

	function showRemoveConfirmation(friendshipId: string, username: string) {
		confirmDialogData = { friendshipId, username };
		showConfirmDialog = true;
	}

	function cancelRemove() {
		showConfirmDialog = false;
		confirmDialogData = null;
	}

	async function confirmRemove() {
		if (!confirmDialogData) return;

		showConfirmDialog = false;
		const { friendshipId } = confirmDialogData;
		confirmDialogData = null;

		try {
			const response = await fetch(`/api/friends/${friendshipId}`, {
				method: 'DELETE',
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to remove friend';
			}
		} catch (err) {
			console.error('Error removing friend:', err);
			error = 'Failed to remove friend';
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			sendFriendRequest();
		}
	}

	function goToChat(username: string) {
		onClose();
		// Navigate to chat with this user
		window.location.href = `/chat?user=${username}`;
	}
</script>

<div class="friends-panel">
	<div class="panel-header">
		<h2>Friends</h2>
		<button class="close-btn" on:click={onClose}>×</button>
	</div>

	<div class="panel-content">
		{#if error}
			<div class="error">{error}</div>
		{/if}

		{#if success}
			<div class="success">{success}</div>
		{/if}

		<!-- Add Friend Section -->
		<div class="add-friend-section">
			<h3>Add Friend</h3>
			<div class="add-friend-form">
				<input
					type="text"
					placeholder="Enter username"
					bind:value={newFriendUsername}
					on:keypress={handleKeyPress}
					disabled={loading}
				/>
				<button on:click={sendFriendRequest} disabled={loading || !newFriendUsername.trim()}>
					{loading ? 'Sending...' : 'Send'}
				</button>
			</div>
		</div>

		<!-- Pending Friend Requests -->
		{#if pendingRequests.length > 0}
			<div class="section">
				<h3>Pending Requests ({pendingRequests.length})</h3>
				<div class="requests-list">
					{#each pendingRequests as request}
						<div class="request-item">
							<div class="user-info">
								{#if request.requester?.profilePicture}
									<img src={request.requester.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{request.requester?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{request.requester?.username}</span>
							</div>
							<div class="request-actions">
								<button class="accept-btn" on:click={() => manageFriendRequest(request._id, 'accept')}>
									✓
								</button>
								<button class="decline-btn" on:click={() => manageFriendRequest(request._id, 'decline')}>
									✗
								</button>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Sent Friend Requests -->
		{#if sentRequests.length > 0}
			<div class="section">
				<h3>Sent Requests ({sentRequests.length})</h3>
				<div class="requests-list">
					{#each sentRequests as request}
						<div class="request-item">
							<div class="user-info">
								{#if request.recipient?.profilePicture}
									<img src={request.recipient.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{request.recipient?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{request.recipient?.username}</span>
							</div>
							<div class="request-status">
								<span class="pending-status">Pending</span>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Friends List -->
		<div class="section">
			<h3>Friends ({friends.length})</h3>
			{#if friends.length === 0}
				<p class="no-friends">No friends yet. Send some friend requests!</p>
			{:else}
				<div class="friends-list">
					{#each friends as friend}
						<div class="friend-item">
							<div class="user-info">
								{#if friend.profilePicture}
									<img src={friend.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{friend.username.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{friend.username}</span>
							</div>
							<div class="friend-actions">
								<button class="message-btn" on:click={() => goToChat(friend.username)}>
									💬
								</button>
								<button class="remove-btn" on:click={() => showRemoveConfirmation(friend.friendshipId, friend.username)}>
									🗑️
								</button>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>

	<!-- Confirmation Dialog -->
	{#if showConfirmDialog && confirmDialogData}
		<div
			class="dialog-overlay"
			role="button"
			tabindex="0"
			on:click={cancelRemove}
			on:keydown={(e) => e.key === 'Escape' && cancelRemove()}
		>
			<div
				class="dialog"
				role="dialog"
				aria-labelledby="dialog-title"
			>
				<div class="dialog-header">
					<h3 id="dialog-title">Remove Friend</h3>
				</div>
				<div class="dialog-content">
					<p>Are you sure you want to remove <strong>{confirmDialogData.username}</strong> from your friends list?</p>
					<p class="dialog-warning">This action cannot be undone.</p>
				</div>
				<div class="dialog-actions">
					<button class="dialog-btn cancel-btn" on:click={cancelRemove}>
						Cancel
					</button>
					<button class="dialog-btn confirm-btn" on:click={confirmRemove}>
						Remove Friend
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.friends-panel {
		height: 100%;
		display: flex;
		flex-direction: column;
		background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
		border-left: 1px solid #e2e8f0;
		backdrop-filter: blur(10px);
	}

	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid #e2e8f0;
		background: rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(10px);
	}

	.panel-header h2 {
		margin: 0;
		font-size: 1.5rem;
		font-weight: 700;
		color: #1e293b;
		letter-spacing: -0.025em;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: #64748b;
		padding: 0.5rem;
		line-height: 1;
		border-radius: 0.5rem;
		transition: all 0.2s ease;
	}

	.close-btn:hover {
		color: #1e293b;
		background: rgba(0, 0, 0, 0.05);
		transform: scale(1.1);
	}

	.panel-content {
		flex: 1;
		overflow-y: auto;
		padding: 1.5rem;
		scrollbar-width: thin;
		scrollbar-color: #cbd5e1 transparent;
	}

	.panel-content::-webkit-scrollbar {
		width: 6px;
	}

	.panel-content::-webkit-scrollbar-track {
		background: transparent;
	}

	.panel-content::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 3px;
	}

	.panel-content::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}

	.error {
		background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
		color: #dc2626;
		padding: 1rem;
		border-radius: 0.75rem;
		margin-bottom: 1.5rem;
		font-size: 0.875rem;
		border: 1px solid #fecaca;
		animation: slideIn 0.3s ease-out;
	}

	.success {
		background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
		color: #16a34a;
		padding: 1rem;
		border-radius: 0.75rem;
		margin-bottom: 1.5rem;
		font-size: 0.875rem;
		border: 1px solid #bbf7d0;
		animation: slideIn 0.3s ease-out;
	}

	.add-friend-section {
		background: rgba(255, 255, 255, 0.7);
		padding: 1.5rem;
		border-radius: 1rem;
		margin-bottom: 2rem;
		border: 1px solid #e2e8f0;
		backdrop-filter: blur(10px);
		transition: all 0.3s ease;
	}

	.add-friend-section:hover {
		transform: translateY(-2px);
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
	}

	.add-friend-section h3 {
		margin: 0 0 1rem 0;
		font-size: 1.125rem;
		font-weight: 600;
		color: #1e293b;
		letter-spacing: -0.025em;
	}

	.add-friend-form {
		display: flex;
		gap: 0.75rem;
	}

	.add-friend-form input {
		flex: 1;
		padding: 0.75rem 1rem;
		border: 2px solid #e2e8f0;
		border-radius: 0.75rem;
		font-size: 0.875rem;
		background: rgba(255, 255, 255, 0.8);
		transition: all 0.2s ease;
		outline: none;
	}

	.add-friend-form input:focus {
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		background: white;
	}

	.add-friend-form button {
		padding: 0.75rem 1.5rem;
		background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
		color: white;
		border: none;
		border-radius: 0.75rem;
		cursor: pointer;
		font-size: 0.875rem;
		font-weight: 600;
		transition: all 0.2s ease;
		white-space: nowrap;
	}

	.add-friend-form button:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
	}

	.add-friend-form button:disabled {
		background: #94a3b8;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	.section {
		margin-bottom: 2rem;
	}

	.section h3 {
		margin: 0 0 1rem 0;
		font-size: 1.125rem;
		font-weight: 600;
		color: #1e293b;
		letter-spacing: -0.025em;
	}

	.requests-list, .friends-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.request-item, .friend-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 0.75rem;
		border: 1px solid #e2e8f0;
		transition: all 0.3s ease;
		backdrop-filter: blur(10px);
	}

	.request-item:hover, .friend-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
		background: rgba(255, 255, 255, 0.95);
	}

	.user-info {
		display: flex;
		align-items: center;
		gap: 0.75rem;
	}

	.profile-pic {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 50%;
		object-fit: cover;
		border: 2px solid #e2e8f0;
		transition: all 0.2s ease;
	}

	.profile-pic-placeholder {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 50%;
		background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		color: #64748b;
		font-size: 1rem;
		border: 2px solid #e2e8f0;
		transition: all 0.2s ease;
	}

	.username {
		font-weight: 600;
		color: #1e293b;
		font-size: 0.875rem;
		letter-spacing: -0.025em;
	}

	.request-actions, .friend-actions {
		display: flex;
		gap: 0.5rem;
	}

	.accept-btn, .message-btn {
		padding: 0.5rem 0.75rem;
		background: linear-gradient(135deg, #10b981 0%, #059669 100%);
		color: white;
		border: none;
		border-radius: 0.5rem;
		cursor: pointer;
		font-size: 0.875rem;
		font-weight: 500;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.accept-btn:hover, .message-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 6px 15px rgba(16, 185, 129, 0.3);
	}

	.decline-btn, .remove-btn {
		padding: 0.5rem 0.75rem;
		background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
		color: white;
		border: none;
		border-radius: 0.5rem;
		cursor: pointer;
		font-size: 0.875rem;
		font-weight: 500;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.decline-btn:hover, .remove-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 6px 15px rgba(239, 68, 68, 0.3);
	}

	.pending-status {
		color: #f59e0b;
		font-weight: 600;
		font-size: 0.875rem;
		padding: 0.25rem 0.75rem;
		background: rgba(245, 158, 11, 0.1);
		border-radius: 0.5rem;
		border: 1px solid rgba(245, 158, 11, 0.2);
	}

	.no-friends {
		color: #64748b;
		font-style: italic;
		text-align: center;
		padding: 2rem;
		font-size: 0.875rem;
		background: rgba(255, 255, 255, 0.5);
		border-radius: 0.75rem;
		border: 1px dashed #cbd5e1;
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	.request-item, .friend-item {
		animation: fadeIn 0.3s ease-out;
	}

	/* Dialog Styles */
	.dialog-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		backdrop-filter: blur(4px);
		animation: fadeIn 0.2s ease-out;
	}

	.dialog {
		background: white;
		border-radius: 1rem;
		box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
		max-width: 400px;
		width: 90%;
		max-height: 90vh;
		overflow: hidden;
		animation: slideIn 0.3s ease-out;
	}

	.dialog-header {
		padding: 1.5rem 1.5rem 0 1.5rem;
	}

	.dialog-header h3 {
		margin: 0;
		font-size: 1.25rem;
		font-weight: 700;
		color: #1e293b;
		letter-spacing: -0.025em;
	}

	.dialog-content {
		padding: 1rem 1.5rem;
	}

	.dialog-content p {
		margin: 0 0 0.75rem 0;
		color: #374151;
		line-height: 1.5;
	}

	.dialog-content p:last-child {
		margin-bottom: 0;
	}

	.dialog-warning {
		color: #dc2626 !important;
		font-size: 0.875rem;
		font-weight: 500;
	}

	.dialog-actions {
		padding: 0 1.5rem 1.5rem 1.5rem;
		display: flex;
		gap: 0.75rem;
		justify-content: flex-end;
	}

	.dialog-btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 0.5rem;
		cursor: pointer;
		font-size: 0.875rem;
		font-weight: 600;
		transition: all 0.2s ease;
	}

	.cancel-btn {
		background: #f1f5f9;
		color: #64748b;
	}

	.cancel-btn:hover {
		background: #e2e8f0;
		color: #475569;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
		color: white;
	}

	.confirm-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 6px 15px rgba(239, 68, 68, 0.3);
	}
</style>
