<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { websocketService } from '$lib/services/websocket';

	interface Friend {
		_id: string;
		username: string;
		profilePicture?: string;
	}

	interface FriendRequest {
		_id: string;
		requester?: Friend;
		recipient?: Friend;
		createdAt: string;
	}

	export let onClose: () => void;

	let friends: Friend[] = [];
	let pendingRequests: FriendRequest[] = [];
	let sentRequests: FriendRequest[] = [];
	let newFriendUsername = '';
	let loading = false;
	let error = '';
	let success = '';

	// Real-time event handlers
	let unsubscribeFriendRequest: (() => void) | null = null;
	let unsubscribeFriendResponse: (() => void) | null = null;
	let unsubscribeFriendshipEstablished: (() => void) | null = null;

	onMount(() => {
		fetchFriends();
		setupRealtimeListeners();
	});

	onDestroy(() => {
		// Clean up real-time listeners
		if (unsubscribeFriendRequest) unsubscribeFriendRequest();
		if (unsubscribeFriendResponse) unsubscribeFriendResponse();
		if (unsubscribeFriendshipEstablished) unsubscribeFriendshipEstablished();
	});

	function setupRealtimeListeners() {
		// Listen for incoming friend requests
		unsubscribeFriendRequest = websocketService.on('friend_request_received', (data) => {
			console.log('Received friend request:', data);
			pendingRequests = [...pendingRequests, data.friendRequest];
			success = `New friend request from ${data.friendRequest.requester.username}`;
			setTimeout(() => success = '', 3000);
		});

		// Listen for friend request responses
		unsubscribeFriendResponse = websocketService.on('friend_request_response', (data) => {
			console.log('Friend request response:', data);
			// Remove from sent requests
			sentRequests = sentRequests.filter(req => req._id !== data.friendRequest._id);
			
			if (data.action === 'accept') {
				success = `${data.friendRequest.recipient.username} accepted your friend request!`;
			} else {
				success = `${data.friendRequest.recipient.username} declined your friend request.`;
			}
			setTimeout(() => success = '', 3000);
		});

		// Listen for new friendships
		unsubscribeFriendshipEstablished = websocketService.on('friendship_established', (data) => {
			console.log('Friendship established:', data);
			// Add to friends list if not already there
			if (!friends.find(f => f._id === data.friend._id)) {
				friends = [...friends, data.friend];
			}
		});
	}

	async function fetchFriends() {
		try {
			const response = await fetch('/api/friends', {
				credentials: 'include'
			});

			if (response.ok) {
				const data = await response.json();
				friends = data.friends || [];
				pendingRequests = data.pendingRequests || [];
				sentRequests = data.sentRequests || [];
			} else {
				error = 'Failed to fetch friends';
			}
		} catch (err) {
			console.error('Error fetching friends:', err);
			error = 'Failed to fetch friends';
		}
	}

	async function sendFriendRequest() {
		if (!newFriendUsername.trim()) {
			error = 'Please enter a username';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/friends', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ username: newFriendUsername.trim() })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				newFriendUsername = '';
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to send friend request';
			}
		} catch (err) {
			console.error('Error sending friend request:', err);
			error = 'Failed to send friend request';
		} finally {
			loading = false;
		}
	}

	async function manageFriendRequest(requestId: string, action: 'accept' | 'decline') {
		try {
			const response = await fetch(`/api/friends/${requestId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ action })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				// Remove from pending requests
				pendingRequests = pendingRequests.filter(req => req._id !== requestId);
				
				// If accepted, refresh friends list
				if (action === 'accept') {
					await fetchFriends();
				}
			} else {
				error = data.error || `Failed to ${action} friend request`;
			}
		} catch (err) {
			console.error(`Error ${action}ing friend request:`, err);
			error = `Failed to ${action} friend request`;
		}
	}

	async function removeFriend(friendshipId: string) {
		if (!confirm('Are you sure you want to remove this friend?')) {
			return;
		}

		try {
			const response = await fetch(`/api/friends/${friendshipId}`, {
				method: 'DELETE',
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to remove friend';
			}
		} catch (err) {
			console.error('Error removing friend:', err);
			error = 'Failed to remove friend';
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			sendFriendRequest();
		}
	}

	function goToChat(username: string) {
		onClose();
		// Navigate to chat with this user
		window.location.href = `/chat?user=${username}`;
	}
</script>

<div class="friends-panel">
	<div class="panel-header">
		<h2>Friends</h2>
		<button class="close-btn" on:click={onClose}>×</button>
	</div>

	<div class="panel-content">
		{#if error}
			<div class="error">{error}</div>
		{/if}

		{#if success}
			<div class="success">{success}</div>
		{/if}

		<!-- Add Friend Section -->
		<div class="add-friend-section">
			<h3>Add Friend</h3>
			<div class="add-friend-form">
				<input
					type="text"
					placeholder="Enter username"
					bind:value={newFriendUsername}
					on:keypress={handleKeyPress}
					disabled={loading}
				/>
				<button on:click={sendFriendRequest} disabled={loading || !newFriendUsername.trim()}>
					{loading ? 'Sending...' : 'Send'}
				</button>
			</div>
		</div>

		<!-- Pending Friend Requests -->
		{#if pendingRequests.length > 0}
			<div class="section">
				<h3>Pending Requests ({pendingRequests.length})</h3>
				<div class="requests-list">
					{#each pendingRequests as request}
						<div class="request-item">
							<div class="user-info">
								{#if request.requester?.profilePicture}
									<img src={request.requester.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{request.requester?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{request.requester?.username}</span>
							</div>
							<div class="request-actions">
								<button class="accept-btn" on:click={() => manageFriendRequest(request._id, 'accept')}>
									✓
								</button>
								<button class="decline-btn" on:click={() => manageFriendRequest(request._id, 'decline')}>
									✗
								</button>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Sent Friend Requests -->
		{#if sentRequests.length > 0}
			<div class="section">
				<h3>Sent Requests ({sentRequests.length})</h3>
				<div class="requests-list">
					{#each sentRequests as request}
						<div class="request-item">
							<div class="user-info">
								{#if request.recipient?.profilePicture}
									<img src={request.recipient.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{request.recipient?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{request.recipient?.username}</span>
							</div>
							<div class="request-status">
								<span class="pending-status">Pending</span>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Friends List -->
		<div class="section">
			<h3>Friends ({friends.length})</h3>
			{#if friends.length === 0}
				<p class="no-friends">No friends yet. Send some friend requests!</p>
			{:else}
				<div class="friends-list">
					{#each friends as friend}
						<div class="friend-item">
							<div class="user-info">
								{#if friend.profilePicture}
									<img src={friend.profilePicture} alt="Profile" class="profile-pic" />
								{:else}
									<div class="profile-pic-placeholder">
										{friend.username.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="username">{friend.username}</span>
							</div>
							<div class="friend-actions">
								<button class="message-btn" on:click={() => goToChat(friend.username)}>
									💬
								</button>
								<button class="remove-btn" on:click={() => removeFriend(friend._id)}>
									🗑️
								</button>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.friends-panel {
		height: 100%;
		display: flex;
		flex-direction: column;
		background: white;
		border-left: 1px solid #e5e7eb;
	}

	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		border-bottom: 1px solid #e5e7eb;
		background: #f9fafb;
	}

	.panel-header h2 {
		margin: 0;
		font-size: 1.25rem;
		font-weight: 600;
		color: #111827;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: #6b7280;
		padding: 0.25rem;
		line-height: 1;
	}

	.close-btn:hover {
		color: #374151;
	}

	.panel-content {
		flex: 1;
		overflow-y: auto;
		padding: 1rem;
	}

	.error {
		background: #fef2f2;
		color: #dc2626;
		padding: 0.75rem;
		border-radius: 0.375rem;
		margin-bottom: 1rem;
		font-size: 0.875rem;
	}

	.success {
		background: #f0fdf4;
		color: #16a34a;
		padding: 0.75rem;
		border-radius: 0.375rem;
		margin-bottom: 1rem;
		font-size: 0.875rem;
	}

	.add-friend-section {
		background: #f9fafb;
		padding: 1rem;
		border-radius: 0.5rem;
		margin-bottom: 1.5rem;
	}

	.add-friend-section h3 {
		margin: 0 0 0.75rem 0;
		font-size: 1rem;
		font-weight: 500;
		color: #374151;
	}

	.add-friend-form {
		display: flex;
		gap: 0.5rem;
	}

	.add-friend-form input {
		flex: 1;
		padding: 0.5rem;
		border: 1px solid #d1d5db;
		border-radius: 0.375rem;
		font-size: 0.875rem;
	}

	.add-friend-form button {
		padding: 0.5rem 1rem;
		background: #3b82f6;
		color: white;
		border: none;
		border-radius: 0.375rem;
		cursor: pointer;
		font-size: 0.875rem;
	}

	.add-friend-form button:disabled {
		background: #9ca3af;
		cursor: not-allowed;
	}

	.section {
		margin-bottom: 1.5rem;
	}

	.section h3 {
		margin: 0 0 0.75rem 0;
		font-size: 1rem;
		font-weight: 500;
		color: #374151;
	}

	.requests-list, .friends-list {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.request-item, .friend-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.75rem;
		background: #f9fafb;
		border-radius: 0.375rem;
		border: 1px solid #e5e7eb;
	}

	.user-info {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.profile-pic {
		width: 2rem;
		height: 2rem;
		border-radius: 50%;
		object-fit: cover;
	}

	.profile-pic-placeholder {
		width: 2rem;
		height: 2rem;
		border-radius: 50%;
		background: #e5e7eb;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 500;
		color: #6b7280;
		font-size: 0.875rem;
	}

	.username {
		font-weight: 500;
		color: #374151;
		font-size: 0.875rem;
	}

	.request-actions, .friend-actions {
		display: flex;
		gap: 0.25rem;
	}

	.accept-btn, .message-btn {
		padding: 0.25rem 0.5rem;
		background: #10b981;
		color: white;
		border: none;
		border-radius: 0.25rem;
		cursor: pointer;
		font-size: 0.75rem;
	}

	.decline-btn, .remove-btn {
		padding: 0.25rem 0.5rem;
		background: #ef4444;
		color: white;
		border: none;
		border-radius: 0.25rem;
		cursor: pointer;
		font-size: 0.75rem;
	}

	.pending-status {
		color: #f59e0b;
		font-weight: 500;
		font-size: 0.75rem;
	}

	.no-friends {
		color: #6b7280;
		font-style: italic;
		text-align: center;
		padding: 1rem;
		font-size: 0.875rem;
	}

	button:hover {
		opacity: 0.9;
	}
</style>
