<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { websocketService } from '$lib/services/websocket';

	interface Friend {
		_id: string;
		username: string;
		profilePicture?: string;
		friendshipId: string;
	}

	interface FriendRequest {
		_id: string;
		requester?: Friend;
		recipient?: Friend;
		createdAt: string;
	}

	export let onClose: () => void;

	let friends: Friend[] = [];
	let pendingRequests: FriendRequest[] = [];
	let sentRequests: FriendRequest[] = [];
	let newFriendUsername = '';
	let loading = false;
	let error = '';
	let success = '';
	let showConfirmDialog = false;
	let confirmDialogData: { friendshipId: string; username: string } | null = null;

	// Real-time event handlers
	let unsubscribeFriendRequest: (() => void) | null = null;
	let unsubscribeFriendResponse: (() => void) | null = null;
	let unsubscribeFriendshipEstablished: (() => void) | null = null;
	let unsubscribeFriendshipRemoved: (() => void) | null = null;

	onMount(() => {
		fetchFriends();
		setupRealtimeListeners();
	});

	onDestroy(() => {
		// Clean up real-time listeners
		if (unsubscribeFriendRequest) unsubscribeFriendRequest();
		if (unsubscribeFriendResponse) unsubscribeFriendResponse();
		if (unsubscribeFriendshipEstablished) unsubscribeFriendshipEstablished();
		if (unsubscribeFriendshipRemoved) unsubscribeFriendshipRemoved();
	});

	function setupRealtimeListeners() {
		// Listen for incoming friend requests
		unsubscribeFriendRequest = websocketService.on('friend_request_received', (data) => {
			console.log('Received friend request:', data);
			pendingRequests = [...pendingRequests, data.friendRequest];
			success = `New friend request from ${data.friendRequest.requester.username}`;
			setTimeout(() => success = '', 3000);
		});

		// Listen for friend request responses
		unsubscribeFriendResponse = websocketService.on('friend_request_response', (data) => {
			console.log('Friend request response:', data);
			// Remove from sent requests
			sentRequests = sentRequests.filter(req => req._id !== data.friendRequest._id);

			if (data.action === 'accept') {
				success = `${data.friendRequest.recipient.username} accepted your friend request!`;
			} else {
				success = `${data.friendRequest.recipient.username} declined your friend request.`;
			}
			setTimeout(() => success = '', 3000);
		});

		// Listen for new friendships
		unsubscribeFriendshipEstablished = websocketService.on('friendship_established', (data) => {
			console.log('Friendship established:', data);
			// Add to friends list if not already there
			if (!friends.find(f => f._id === data.friend._id)) {
				friends = [...friends, data.friend];
			}
		});

		// Listen for friendship removals
		unsubscribeFriendshipRemoved = websocketService.on('friendship_removed', (data) => {
			console.log('Friendship removed:', data);
			// Remove from friends list
			friends = friends.filter(f => f._id !== data.removedFriend._id);
			success = `${data.removedFriend.username} has been removed from your friends list`;
			setTimeout(() => success = '', 3000);
		});
	}

	async function fetchFriends() {
		try {
			const response = await fetch('/api/friends', {
				credentials: 'include'
			});

			if (response.ok) {
				const data = await response.json();
				friends = data.friends || [];
				pendingRequests = data.pendingRequests || [];
				sentRequests = data.sentRequests || [];
			} else {
				error = 'Failed to fetch friends';
			}
		} catch (err) {
			console.error('Error fetching friends:', err);
			error = 'Failed to fetch friends';
		}
	}

	async function sendFriendRequest() {
		if (!newFriendUsername.trim()) {
			error = 'Please enter a username';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/friends', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ username: newFriendUsername.trim() })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				newFriendUsername = '';
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to send friend request';
			}
		} catch (err) {
			console.error('Error sending friend request:', err);
			error = 'Failed to send friend request';
		} finally {
			loading = false;
		}
	}

	async function manageFriendRequest(requestId: string, action: 'accept' | 'decline') {
		try {
			const response = await fetch(`/api/friends/${requestId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ action })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				// Remove from pending requests
				pendingRequests = pendingRequests.filter(req => req._id !== requestId);

				// If accepted, refresh friends list
				if (action === 'accept') {
					await fetchFriends();
				}
			} else {
				error = data.error || `Failed to ${action} friend request`;
			}
		} catch (err) {
			console.error(`Error ${action}ing friend request:`, err);
			error = `Failed to ${action} friend request`;
		}
	}

	function showRemoveConfirmation(friendshipId: string, username: string) {
		confirmDialogData = { friendshipId, username };
		showConfirmDialog = true;
	}

	function cancelRemove() {
		showConfirmDialog = false;
		confirmDialogData = null;
	}

	async function confirmRemove() {
		if (!confirmDialogData) return;

		showConfirmDialog = false;
		const { friendshipId } = confirmDialogData;
		confirmDialogData = null;

		try {
			const response = await fetch(`/api/friends/${friendshipId}`, {
				method: 'DELETE',
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to remove friend';
			}
		} catch (err) {
			console.error('Error removing friend:', err);
			error = 'Failed to remove friend';
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			sendFriendRequest();
		}
	}

	function goToChat(username: string) {
		onClose();
		// Navigate to chat with this user
		window.location.href = `/chat?user=${username}`;
	}
</script>

<div class="flex h-full flex-col bg-white border-l border-gray-200">
	<div class="flex h-16 flex-shrink-0 items-center justify-between border-b border-gray-200 px-4 bg-white">
		<h2 class="font-mono text-xl font-semibold text-black">Friends</h2>
		<button class="text-gray-500 hover:text-black transition-colors" on:click={onClose}>×</button>
	</div>

	<div class="flex-1 overflow-y-auto p-4">
		{#if error}
			<div class="bg-red-50 text-red-600 p-3 rounded border border-red-200 mb-4 text-sm">{error}</div>
		{/if}

		{#if success}
			<div class="bg-green-50 text-green-600 p-3 rounded border border-green-200 mb-4 text-sm">{success}</div>
		{/if}

		<!-- Add Friend Section -->
		<div class="bg-gray-50 p-4 rounded border border-gray-200 mb-4">
			<h3 class="font-mono text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider">Add Friend</h3>
			<div class="flex gap-2">
				<input
					type="text"
					placeholder="Enter username"
					bind:value={newFriendUsername}
					on:keypress={handleKeyPress}
					disabled={loading}
					class="flex-1 rounded border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
				/>
				<button
					on:click={sendFriendRequest}
					disabled={loading || !newFriendUsername.trim()}
					class="rounded bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed"
				>
					{loading ? 'Sending...' : 'Send'}
				</button>
			</div>
		</div>

		<!-- Pending Friend Requests -->
		{#if pendingRequests.length > 0}
			<div class="mb-4">
				<h3 class="font-mono text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider">Pending Requests ({pendingRequests.length})</h3>
				<div class="space-y-2">
					{#each pendingRequests as request}
						<div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded">
							<div class="flex items-center gap-3">
								{#if request.requester?.profilePicture}
									<img src={request.requester.profilePicture} alt="Profile" class="w-8 h-8 rounded-full object-cover" />
								{:else}
									<div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-xs font-semibold text-gray-600 uppercase">
										{request.requester?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="font-mono text-sm text-gray-700">{request.requester?.username}</span>
							</div>
							<div class="flex gap-2">
								<button
									class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
									on:click={() => manageFriendRequest(request._id, 'accept')}
								>
									✓
								</button>
								<button
									class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
									on:click={() => manageFriendRequest(request._id, 'decline')}
								>
									✗
								</button>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Sent Friend Requests -->
		{#if sentRequests.length > 0}
			<div class="mb-4">
				<h3 class="font-mono text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider">Sent Requests ({sentRequests.length})</h3>
				<div class="space-y-2">
					{#each sentRequests as request}
						<div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded">
							<div class="flex items-center gap-3">
								{#if request.recipient?.profilePicture}
									<img src={request.recipient.profilePicture} alt="Profile" class="w-8 h-8 rounded-full object-cover" />
								{:else}
									<div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-xs font-semibold text-gray-600 uppercase">
										{request.recipient?.username?.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="font-mono text-sm text-gray-700">{request.recipient?.username}</span>
							</div>
							<div>
								<span class="text-xs text-yellow-600 font-medium bg-yellow-50 px-2 py-1 rounded border border-yellow-200">Pending</span>
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Friends List -->
		<div class="mb-4">
			<h3 class="font-mono text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider">Friends ({friends.length})</h3>
			{#if friends.length === 0}
				<p class="text-center text-gray-500 text-sm italic p-4 bg-gray-50 rounded border border-gray-200">No friends yet. Send some friend requests!</p>
			{:else}
				<div class="space-y-2">
					{#each friends as friend}
						<div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors">
							<div class="flex items-center gap-3">
								{#if friend.profilePicture}
									<img src={friend.profilePicture} alt="Profile" class="w-8 h-8 rounded-full object-cover" />
								{:else}
									<div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-xs font-semibold text-gray-600 uppercase">
										{friend.username.charAt(0).toUpperCase()}
									</div>
								{/if}
								<span class="font-mono text-sm text-gray-700">{friend.username}</span>
							</div>
							<div class="flex gap-2">
								<button
									class="px-3 py-1 bg-black text-white rounded text-sm hover:bg-gray-800 transition-colors"
									on:click={() => goToChat(friend.username)}
								>
									💬
								</button>
								<button
									class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
									on:click={() => showRemoveConfirmation(friend.friendshipId, friend.username)}
								>
									🗑️
								</button>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>

	<!-- Confirmation Dialog -->
	{#if showConfirmDialog && confirmDialogData}
		<div
			class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
			role="button"
			tabindex="0"
			on:click={cancelRemove}
			on:keydown={(e) => e.key === 'Escape' && cancelRemove()}
		>
			<div
				class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
				role="dialog"
				aria-labelledby="dialog-title"
			>
				<div class="p-6">
					<h3 id="dialog-title" class="font-mono text-lg font-semibold text-black mb-4">Remove Friend</h3>
					<p class="text-gray-700 mb-2">Are you sure you want to remove <strong>{confirmDialogData.username}</strong> from your friends list?</p>
					<p class="text-red-600 text-sm font-medium">This action cannot be undone.</p>
				</div>
				<div class="flex gap-3 p-6 pt-0">
					<button
						class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded font-mono text-sm hover:bg-gray-200 transition-colors"
						on:click={cancelRemove}
					>
						Cancel
					</button>
					<button
						class="flex-1 px-4 py-2 bg-red-600 text-white rounded font-mono text-sm hover:bg-red-700 transition-colors"
						on:click={confirmRemove}
					>
						Remove Friend
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>


