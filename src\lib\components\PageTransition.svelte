<script lang="ts">
	import { onMount } from 'svelte';
	import { fade, fly } from 'svelte/transition';
	import { page } from '$app/stores';

	export let duration = 300;
	export let delay = 0;

	let mounted = false;
	let currentPath = '';

	// Track page changes for transitions
	$: if ($page.url.pathname !== currentPath) {
		currentPath = $page.url.pathname;
		mounted = false;
		// Small delay to allow for smooth transition
		setTimeout(() => {
			mounted = true;
		}, 50);
	}

	onMount(() => {
		mounted = true;
	});
</script>

{#if mounted}
	<div
		in:fly={{ y: 20, duration, delay }}
		out:fade={{ duration: duration / 2 }}
		class="min-h-screen"
	>
		<slot />
	</div>
{/if}
