<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import ConfirmDialog from './ConfirmDialog.svelte';

	export let currentImageUrl: string | null = null;
	export let username: string = '';
	export let loading = false;

	const dispatch = createEventDispatcher<{
		fileUploaded: { key: string; url: string };
		removeImage: void;
		error: { message: string };
	}>();

	let fileInput: HTMLInputElement;
	let previewUrl: string | null = currentImageUrl;
	let showRemoveDialog = false;
	let dragOver = false;

	// Update preview when currentImageUrl changes
	$: previewUrl = currentImageUrl;

	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			processFile(input.files[0]);
		}
	}

	function handleDrop(event: DragEvent) {
		event.preventDefault();
		dragOver = false;

		if (loading) return;

		const files = event.dataTransfer?.files;
		if (files && files[0]) {
			processFile(files[0]);
		}
	}

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		if (!loading) {
			dragOver = true;
		}
	}

	function handleDragLeave() {
		dragOver = false;
	}

	async function processFile(file: File) {
		// Validate file type
		if (!file.type.startsWith('image/')) {
			dispatch('error', { message: 'Please select an image file.' });
			return;
		}

		// Validate file size (5MB max)
		const maxSize = 5 * 1024 * 1024;
		if (file.size > maxSize) {
			dispatch('error', { message: 'Image must be smaller than 5MB.' });
			return;
		}

		// Create preview immediately
		previewUrl = URL.createObjectURL(file);
		loading = true;

		try {
			// Upload file using the same API as attachments
			const formData = new FormData();
			formData.append('file', file);
			formData.append('type', 'avatar');

			const response = await fetch('/api/upload', {
				method: 'POST',
				credentials: 'include',
				body: formData
			});

			if (response.ok) {
				const result = await response.json();
				// Dispatch upload success with key and URL
				dispatch('fileUploaded', {
					key: result.file.key,
					url: `/api/files?key=${encodeURIComponent(result.file.key)}`
				});
			} else {
				const error = await response.json();
				dispatch('error', { message: error.error || 'Upload failed' });
				// Reset preview on error
				previewUrl = currentImageUrl;
			}
		} catch (err) {
			console.error('Upload error:', err);
			dispatch('error', { message: 'Upload failed' });
			// Reset preview on error
			previewUrl = currentImageUrl;
		} finally {
			loading = false;
			// Clear input
			if (fileInput) {
				fileInput.value = '';
			}
		}
	}

	function openFileDialog() {
		if (!loading) {
			fileInput?.click();
		}
	}

	function handleRemoveImage() {
		showRemoveDialog = true;
	}

	function confirmRemoveImage() {
		previewUrl = null;
		showRemoveDialog = false;
		dispatch('removeImage');
	}

	function cancelRemoveImage() {
		showRemoveDialog = false;
	}
</script>

<input
	bind:this={fileInput}
	type="file"
	accept="image/*"
	on:change={handleFileSelect}
	class="hidden"
	disabled={loading}
/>

<div class="space-y-4">
	<!-- Profile Picture Display -->
	<div class="flex items-center gap-6">
		<!-- Avatar -->
		<div
			class="relative h-24 w-24 overflow-hidden rounded-full bg-gray-200 transition-all duration-200 {dragOver ? 'ring-2 ring-blue-500 ring-offset-2' : ''}"
			on:drop={handleDrop}
			on:dragover={handleDragOver}
			on:dragleave={handleDragLeave}
			role="button"
			tabindex="0"
			on:click={openFileDialog}
			on:keydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					e.preventDefault();
					openFileDialog();
				}
			}}
		>
			{#if previewUrl}
				<img src={previewUrl} alt={username} class="h-full w-full object-cover" />
				<!-- Loading overlay -->
				{#if loading}
					<div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
						<div class="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
					</div>
				{/if}
			{:else}
				<div class="flex h-full w-full items-center justify-center bg-gray-300 text-gray-600 transition-colors hover:bg-gray-400">
					{#if loading}
						<div class="h-6 w-6 animate-spin rounded-full border-2 border-gray-600 border-t-transparent"></div>
					{:else if dragOver}
						<svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
						</svg>
					{:else}
						<span class="text-2xl font-semibold uppercase">
							{username ? username[0] : '?'}
						</span>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Controls -->
		<div class="space-y-2">
			<button
				type="button"
				class="rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
				on:click={openFileDialog}
				disabled={loading}
			>
				{previewUrl ? 'Change Picture' : 'Upload Picture'}
			</button>

			{#if previewUrl}
				<button
					type="button"
					class="block rounded-md border border-red-300 bg-white px-4 py-2 font-mono text-sm text-red-600 transition-colors hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50"
					on:click={handleRemoveImage}
					disabled={loading}
				>
					Remove Picture
				</button>
			{/if}
		</div>
	</div>

	<!-- Help Text -->
	<p class="text-xs text-gray-500">
		Click or drag and drop an image. Maximum size: 5MB. Supported formats: JPG, PNG, GIF, WebP.
	</p>
</div>

<!-- Remove Confirmation Dialog -->
<ConfirmDialog
	bind:isOpen={showRemoveDialog}
	title="Remove Profile Picture"
	message="Are you sure you want to remove your profile picture? This action cannot be undone."
	confirmText="Remove"
	cancelText="Cancel"
	confirmVariant="danger"
	on:confirm={confirmRemoveImage}
	on:cancel={cancelRemoveImage}
/>
