<script lang="ts">
	import { onMount } from 'svelte';
	import { auth } from '$lib/stores/auth';
	import { get } from 'svelte/store';

	export let onClose: () => void;

	let currentPassword = '';
	let newPassword = '';
	let confirmPassword = '';
	let profilePictureFile: File | null = null;
	let profilePicturePreview = '';
	let loading = false;
	let error = '';
	let success = '';

	let currentUser = get(auth);

	onMount(() => {
		// Subscribe to auth changes
		const unsubscribe = auth.subscribe(value => {
			currentUser = value;
		});

		return unsubscribe;
	});

	async function handlePasswordChange() {
		if (!currentPassword || !newPassword || !confirmPassword) {
			error = 'All password fields are required';
			return;
		}

		if (newPassword !== confirmPassword) {
			error = 'New passwords do not match';
			return;
		}

		if (newPassword.length < 6) {
			error = 'New password must be at least 6 characters';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/user/password', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({
					currentPassword,
					newPassword
				})
			});

			const data = await response.json();

			if (response.ok) {
				success = 'Password updated successfully';
				currentPassword = '';
				newPassword = '';
				confirmPassword = '';
			} else {
				error = data.error || 'Failed to update password';
			}
		} catch (err) {
			console.error('Error updating password:', err);
			error = 'Failed to update password';
		} finally {
			loading = false;
		}
	}

	async function handleProfilePictureChange() {
		if (!profilePictureFile) {
			error = 'Please select a profile picture';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const formData = new FormData();
			formData.append('profilePicture', profilePictureFile);

			const response = await fetch('/api/user/profile', {
				method: 'PUT',
				credentials: 'include',
				body: formData
			});

			const data = await response.json();

			if (response.ok) {
				success = 'Profile picture updated successfully';
				profilePictureFile = null;
				profilePicturePreview = '';
				
				// Update auth store with new profile picture
				auth.update(current => ({
					...current,
					profilePicture: data.profilePicture
				}));
			} else {
				error = data.error || 'Failed to update profile picture';
			}
		} catch (err) {
			console.error('Error updating profile picture:', err);
			error = 'Failed to update profile picture';
		} finally {
			loading = false;
		}
	}

	async function removeProfilePicture() {
		if (!confirm('Are you sure you want to remove your profile picture?')) {
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/user/profile', {
				method: 'DELETE',
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok) {
				success = 'Profile picture removed successfully';
				
				// Update auth store
				auth.update(current => ({
					...current,
					profilePicture: null
				}));
			} else {
				error = data.error || 'Failed to remove profile picture';
			}
		} catch (err) {
			console.error('Error removing profile picture:', err);
			error = 'Failed to remove profile picture';
		} finally {
			loading = false;
		}
	}

	function handleFileSelect(event: Event) {
		const target = event.target as HTMLInputElement;
		const file = target.files?.[0];
		
		if (file) {
			// Validate file type
			if (!file.type.startsWith('image/')) {
				error = 'Please select an image file';
				return;
			}

			// Validate file size (5MB max)
			if (file.size > 5 * 1024 * 1024) {
				error = 'File size must be less than 5MB';
				return;
			}

			profilePictureFile = file;
			
			// Create preview
			const reader = new FileReader();
			reader.onload = (e) => {
				profilePicturePreview = e.target?.result as string;
			};
			reader.readAsDataURL(file);
			
			error = '';
		}
	}

	async function logout() {
		try {
			const response = await fetch('/api/auth/logout', {
				method: 'POST',
				credentials: 'include'
			});

			if (response.ok) {
				// Clear auth store
				auth.set({
					isAuthenticated: false,
					username: null,
					token: null,
					profilePicture: null
				});

				// Redirect to login
				window.location.href = '/login';
			} else {
				error = 'Failed to logout';
			}
		} catch (err) {
			console.error('Error logging out:', err);
			error = 'Failed to logout';
		}
	}
</script>

<div class="settings-panel">
	<div class="panel-header">
		<h2>Settings</h2>
		<button class="close-btn" on:click={onClose}>×</button>
	</div>

	<div class="panel-content">
		{#if error}
			<div class="error">{error}</div>
		{/if}

		{#if success}
			<div class="success">{success}</div>
		{/if}

		<!-- Profile Section -->
		<div class="section">
			<h3>Profile</h3>
			<div class="profile-info">
				<div class="current-profile">
					{#if currentUser.profilePicture}
						<img src={currentUser.profilePicture} alt="Profile" class="current-profile-pic" />
					{:else}
						<div class="profile-pic-placeholder">
							{currentUser.username?.charAt(0).toUpperCase() || 'U'}
						</div>
					{/if}
					<div class="profile-details">
						<p class="username">@{currentUser.username}</p>
						{#if currentUser.profilePicture}
							<button class="remove-pic-btn" on:click={removeProfilePicture} disabled={loading}>
								Remove Picture
							</button>
						{/if}
					</div>
				</div>

				<div class="profile-picture-section">
					<h4>Change Profile Picture</h4>
					<input
						type="file"
						accept="image/*"
						on:change={handleFileSelect}
						disabled={loading}
					/>
					
					{#if profilePicturePreview}
						<div class="preview">
							<img src={profilePicturePreview} alt="Preview" class="preview-pic" />
							<button on:click={handleProfilePictureChange} disabled={loading}>
								{loading ? 'Uploading...' : 'Update Picture'}
							</button>
						</div>
					{/if}
				</div>
			</div>
		</div>

		<!-- Password Section -->
		<div class="section">
			<h3>Change Password</h3>
			<div class="password-form">
				<div class="form-group">
					<label for="currentPassword">Current Password</label>
					<input
						id="currentPassword"
						type="password"
						bind:value={currentPassword}
						disabled={loading}
						placeholder="Enter current password"
					/>
				</div>

				<div class="form-group">
					<label for="newPassword">New Password</label>
					<input
						id="newPassword"
						type="password"
						bind:value={newPassword}
						disabled={loading}
						placeholder="Enter new password"
					/>
				</div>

				<div class="form-group">
					<label for="confirmPassword">Confirm New Password</label>
					<input
						id="confirmPassword"
						type="password"
						bind:value={confirmPassword}
						disabled={loading}
						placeholder="Confirm new password"
					/>
				</div>

				<button class="update-btn" on:click={handlePasswordChange} disabled={loading}>
					{loading ? 'Updating...' : 'Update Password'}
				</button>
			</div>
		</div>

		<!-- Account Section -->
		<div class="section">
			<h3>Account</h3>
			<div class="account-actions">
				<button class="logout-btn" on:click={logout}>
					Logout
				</button>
			</div>
		</div>
	</div>
</div>

<style>
	.settings-panel {
		height: 100%;
		display: flex;
		flex-direction: column;
		background: white;
		border-left: 1px solid #e5e7eb;
	}

	.panel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		border-bottom: 1px solid #e5e7eb;
		background: #f9fafb;
	}

	.panel-header h2 {
		margin: 0;
		font-size: 1.25rem;
		font-weight: 600;
		color: #111827;
	}

	.close-btn {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: #6b7280;
		padding: 0.25rem;
		line-height: 1;
	}

	.close-btn:hover {
		color: #374151;
	}

	.panel-content {
		flex: 1;
		overflow-y: auto;
		padding: 1rem;
	}

	.error {
		background: #fef2f2;
		color: #dc2626;
		padding: 0.75rem;
		border-radius: 0.375rem;
		margin-bottom: 1rem;
		font-size: 0.875rem;
	}

	.success {
		background: #f0fdf4;
		color: #16a34a;
		padding: 0.75rem;
		border-radius: 0.375rem;
		margin-bottom: 1rem;
		font-size: 0.875rem;
	}

	.section {
		margin-bottom: 2rem;
		padding-bottom: 1.5rem;
		border-bottom: 1px solid #e5e7eb;
	}

	.section:last-child {
		border-bottom: none;
	}

	.section h3 {
		margin: 0 0 1rem 0;
		font-size: 1.125rem;
		font-weight: 600;
		color: #374151;
	}

	.section h4 {
		margin: 0 0 0.5rem 0;
		font-size: 1rem;
		font-weight: 500;
		color: #374151;
	}

	.current-profile {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 1.5rem;
	}

	.current-profile-pic {
		width: 4rem;
		height: 4rem;
		border-radius: 50%;
		object-fit: cover;
	}

	.profile-pic-placeholder {
		width: 4rem;
		height: 4rem;
		border-radius: 50%;
		background: #e5e7eb;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		color: #6b7280;
		font-size: 1.5rem;
	}

	.username {
		margin: 0 0 0.5rem 0;
		font-weight: 600;
		color: #374151;
	}

	.remove-pic-btn {
		padding: 0.25rem 0.75rem;
		background: #ef4444;
		color: white;
		border: none;
		border-radius: 0.375rem;
		cursor: pointer;
		font-size: 0.875rem;
	}

	.profile-picture-section input {
		width: 100%;
		padding: 0.5rem;
		border: 1px solid #d1d5db;
		border-radius: 0.375rem;
		margin-bottom: 1rem;
	}

	.preview {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.preview-pic {
		width: 3rem;
		height: 3rem;
		border-radius: 50%;
		object-fit: cover;
	}

	.form-group {
		margin-bottom: 1rem;
	}

	.form-group label {
		display: block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		color: #374151;
		font-size: 0.875rem;
	}

	.form-group input {
		width: 100%;
		padding: 0.75rem;
		border: 1px solid #d1d5db;
		border-radius: 0.375rem;
		font-size: 0.875rem;
	}

	.update-btn, .logout-btn {
		padding: 0.75rem 1.5rem;
		border: none;
		border-radius: 0.375rem;
		cursor: pointer;
		font-weight: 500;
		font-size: 0.875rem;
	}

	.update-btn {
		background: #3b82f6;
		color: white;
	}

	.update-btn:disabled {
		background: #9ca3af;
		cursor: not-allowed;
	}

	.logout-btn {
		background: #ef4444;
		color: white;
	}

	button:hover:not(:disabled) {
		opacity: 0.9;
	}
</style>
