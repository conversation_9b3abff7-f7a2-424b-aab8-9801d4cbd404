import type { RequestEvent } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import rateLimitService, { getClientIdentifier, createRateLimitResponse, type RateLimitConfig } from '$lib/services/rateLimit';
import { ENV } from '$lib/config/env';

/**
 * Rate limiting middleware for API endpoints
 */
export function withRateLimit(
    config: RateLimitConfig,
    handler: (event: RequestEvent) => Promise<Response> | Response
) {
    return async (event: RequestEvent): Promise<Response> => {
        try {
            // Get user ID from token if available
            let userId: string | undefined;
            const token = event.cookies.get('token') || event.url.searchParams.get('token');
            
            if (token) {
                try {
                    const decoded = jwt.verify(token, ENV.JWT_SECRET) as any;
                    userId = decoded.userId;
                } catch {
                    // Invalid token, continue with IP-based rate limiting
                }
            }
            
            // Get client identifier
            const identifier = getClientIdentifier(event.request, userId);
            
            // Check rate limit
            const result = rateLimitService.checkLimit(identifier, config);
            
            if (!result.allowed) {
                console.warn(`Rate limit exceeded for ${identifier}`);
                return createRateLimitResponse(
                    result.remaining,
                    result.resetTime,
                    'Too many requests. Please try again later.'
                );
            }
            
            // Add rate limit headers to successful responses
            const response = await handler(event);
            
            // Add rate limit headers
            response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
            response.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());
            
            return response;
            
        } catch (error) {
            console.error('Rate limiting error:', error);
            // If rate limiting fails, allow the request to proceed
            return handler(event);
        }
    };
}

/**
 * Rate limiting middleware specifically for authentication endpoints
 * Uses IP-based limiting since user may not be authenticated yet
 */
export function withAuthRateLimit(
    config: RateLimitConfig,
    handler: (event: RequestEvent) => Promise<Response> | Response
) {
    return async (event: RequestEvent): Promise<Response> => {
        try {
            // Use IP-based identifier for auth endpoints
            const identifier = getClientIdentifier(event.request);
            
            // Check rate limit
            const result = rateLimitService.checkLimit(identifier, config);
            
            if (!result.allowed) {
                console.warn(`Auth rate limit exceeded for ${identifier}`);
                return createRateLimitResponse(
                    result.remaining,
                    result.resetTime,
                    'Too many authentication attempts. Please try again later.'
                );
            }
            
            // Execute handler
            const response = await handler(event);
            
            // Add rate limit headers
            response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
            response.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());
            
            return response;
            
        } catch (error) {
            console.error('Auth rate limiting error:', error);
            // If rate limiting fails, allow the request to proceed
            return handler(event);
        }
    };
}
