import mongoose, { Schema, Document } from 'mongoose';

export interface IFriend extends Document {
    requester: mongoose.Types.ObjectId; // User who sent the friend request
    recipient: mongoose.Types.ObjectId; // User who received the friend request
    status: 'pending' | 'accepted' | 'declined' | 'blocked';
    createdAt: Date;
    updatedAt: Date;
}

const FriendSchema = new Schema<IFriend>({
    requester: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    recipient: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    status: {
        type: String,
        enum: ['pending', 'accepted', 'declined', 'blocked'],
        default: 'pending'
    }
}, {
    timestamps: true
});

// Compound index to ensure unique friend relationships
FriendSchema.index({ requester: 1, recipient: 1 }, { unique: true });

// Index for efficient queries
FriendSchema.index({ requester: 1, status: 1 });
FriendSchema.index({ recipient: 1, status: 1 });

// Static methods for common operations
FriendSchema.statics.areFriends = async function(userId1: string, userId2: string): Promise<boolean> {
    const friendship = await this.findOne({
        $or: [
            { requester: userId1, recipient: userId2, status: 'accepted' },
            { requester: userId2, recipient: userId1, status: 'accepted' }
        ]
    });
    return !!friendship;
};

FriendSchema.statics.getFriendship = async function(userId1: string, userId2: string) {
    return await this.findOne({
        $or: [
            { requester: userId1, recipient: userId2 },
            { requester: userId2, recipient: userId1 }
        ]
    });
};

FriendSchema.statics.getFriends = async function(userId: string) {
    const friendships = await this.find({
        $or: [
            { requester: userId, status: 'accepted' },
            { recipient: userId, status: 'accepted' }
        ]
    }).populate('requester', 'username profilePictureKey')
      .populate('recipient', 'username profilePictureKey');

    // Return the friend (not the current user) from each friendship along with friendship ID
    return friendships.map((friendship: any) => {
        const friend = friendship.requester._id.toString() === userId
            ? friendship.recipient
            : friendship.requester;

        return {
            ...friend.toObject(),
            friendshipId: friendship._id // Include the friendship ID for removal
        };
    });
};

FriendSchema.statics.getPendingRequests = async function(userId: string) {
    return await this.find({
        recipient: userId,
        status: 'pending'
    }).populate('requester', 'username profilePictureKey');
};

FriendSchema.statics.getSentRequests = async function(userId: string) {
    return await this.find({
        requester: userId,
        status: 'pending'
    }).populate('recipient', 'username profilePictureKey');
};

// Prevent users from friending themselves
FriendSchema.pre('save', function(next) {
    if (this.requester.toString() === this.recipient.toString()) {
        next(new Error('Cannot send friend request to yourself'));
    } else {
        next();
    }
});

const Friend = mongoose.models.Friend || mongoose.model<IFriend>('Friend', FriendSchema);

export default Friend;
