import mongoose, { Schema, Document } from 'mongoose';
import Chat from './Chat';
import r2Storage from '../services/r2Storage';

interface I<PERSON>ser extends Document {
	username: string;
	password: string;
	profilePicture?: string; // Virtual field - computed from profilePictureKey
	profilePictureKey?: string; // Actual R2 key stored in database
}

const UserSchema = new Schema<IUser>({
	username: { type: String, required: true, unique: true },
	password: { type: String, required: true },
	profilePictureKey: { type: String } // Store R2 key instead of full URL
});

// Virtual field to generate public profile picture URL
UserSchema.virtual('profilePicture').get(function() {
	if (!this.profilePictureKey) {
		return undefined;
	}
	// Use the same secure API endpoint as attachments
	return `/api/files?key=${encodeURIComponent(this.profilePictureKey)}`;
});

// Make sure virtuals are included in JSON output
UserSchema.set('toJSON', { virtuals: true });
UserSchema.set('toObject', { virtuals: true });

// Delete user's messages and files when user is deleted
UserSchema.pre('deleteOne', async function (next) {
	try {
		const userId = this.getQuery()._id;

		// Get the user to access their profile picture
		const user = await mongoose.model('User').findById(userId);

		// Delete user's profile picture from R2
		if (user && user.profilePictureKey) {
			try {
				await r2Storage.deleteFile(user.profilePictureKey);
				console.log(`Deleted profile picture for user ${userId}: ${user.profilePictureKey}`);
			} catch (deleteError) {
				console.warn(`Failed to delete profile picture for user ${userId}:`, deleteError);
			}
		}

		// Find all messages with attachments sent by this user
		const messagesWithAttachments = await Chat.find({
			sender: userId,
			attachments: { $exists: true, $not: { $size: 0 } }
		});

		// Delete attachments from R2 storage
		if (messagesWithAttachments.length > 0) {
			console.log(`Deleting attachments from ${messagesWithAttachments.length} messages by user ${userId}`);

			for (const message of messagesWithAttachments) {
				if (message.attachments && message.attachments.length > 0) {
					for (const attachment of message.attachments) {
						if (attachment.key) {
							try {
								await r2Storage.deleteFile(attachment.key);
								console.log(`Deleted user attachment: ${attachment.key}`);
							} catch (deleteError) {
								console.warn(`Failed to delete user attachment ${attachment.key}:`, deleteError);
								// Continue with other attachments even if one fails
							}
						}
					}
				}
			}
		}

		// Delete user's messages
		await Chat.deleteMany({
			$or: [{ sender: userId }, { receiver: userId }]
		});

		next();
	} catch (error) {
		next(error as Error);
	}
});

const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
