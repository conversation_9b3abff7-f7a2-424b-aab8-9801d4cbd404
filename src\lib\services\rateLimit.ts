// Rate limiting service using in-memory storage
// For production, consider using Redis for distributed rate limiting

interface RateLimitEntry {
    count: number;
    resetTime: number;
}

interface RateLimitConfig {
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Maximum requests per window
}

class RateLimitService {
    private storage = new Map<string, RateLimitEntry>();
    private cleanupInterval: NodeJS.Timeout;

    constructor() {
        // Clean up expired entries every 5 minutes
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, 5 * 60 * 1000);
    }

    private cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.storage.entries()) {
            if (now > entry.resetTime) {
                this.storage.delete(key);
            }
        }
    }

    /**
     * Check if a request should be rate limited
     * @param identifier - Unique identifier (IP, user ID, etc.)
     * @param config - Rate limit configuration
     * @returns { allowed: boolean, remaining: number, resetTime: number }
     */
    checkLimit(identifier: string, config: RateLimitConfig): {
        allowed: boolean;
        remaining: number;
        resetTime: number;
    } {
        const now = Date.now();
        const key = identifier;
        
        let entry = this.storage.get(key);
        
        // If no entry exists or window has expired, create new entry
        if (!entry || now > entry.resetTime) {
            entry = {
                count: 1,
                resetTime: now + config.windowMs
            };
            this.storage.set(key, entry);
            
            return {
                allowed: true,
                remaining: config.maxRequests - 1,
                resetTime: entry.resetTime
            };
        }
        
        // Increment count
        entry.count++;
        
        const allowed = entry.count <= config.maxRequests;
        const remaining = Math.max(0, config.maxRequests - entry.count);
        
        return {
            allowed,
            remaining,
            resetTime: entry.resetTime
        };
    }

    /**
     * Get current rate limit status without incrementing
     */
    getStatus(identifier: string, config: RateLimitConfig): {
        count: number;
        remaining: number;
        resetTime: number;
    } {
        const now = Date.now();
        const entry = this.storage.get(identifier);
        
        if (!entry || now > entry.resetTime) {
            return {
                count: 0,
                remaining: config.maxRequests,
                resetTime: now + config.windowMs
            };
        }
        
        return {
            count: entry.count,
            remaining: Math.max(0, config.maxRequests - entry.count),
            resetTime: entry.resetTime
        };
    }

    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.storage.clear();
    }
}

// Rate limit configurations for different endpoints
export const RATE_LIMITS = {
    // Authentication endpoints
    LOGIN: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 attempts per 15 minutes
    REGISTER: { windowMs: 60 * 60 * 1000, maxRequests: 3 }, // 3 registrations per hour
    
    // Messaging endpoints
    SEND_MESSAGE: { windowMs: 60 * 1000, maxRequests: 30 }, // 30 messages per minute
    GET_MESSAGES: { windowMs: 60 * 1000, maxRequests: 60 }, // 60 requests per minute
    DELETE_MESSAGE: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 deletions per minute
    
    // File upload endpoints
    UPLOAD_FILE: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 uploads per minute
    
    // User management endpoints
    UPDATE_PROFILE: { windowMs: 60 * 1000, maxRequests: 5 }, // 5 updates per minute
    CHANGE_PASSWORD: { windowMs: 60 * 1000, maxRequests: 3 }, // 3 password changes per minute
    
    // Group management endpoints
    CREATE_GROUP: { windowMs: 60 * 1000, maxRequests: 5 }, // 5 group creations per minute
    DELETE_GROUP: { windowMs: 60 * 1000, maxRequests: 3 }, // 3 group deletions per minute
    
    // General API endpoints
    GENERAL: { windowMs: 60 * 1000, maxRequests: 100 }, // 100 requests per minute
    
    // Friend system endpoints (to be implemented)
    SEND_FRIEND_REQUEST: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 friend requests per minute
    ACCEPT_FRIEND_REQUEST: { windowMs: 60 * 1000, maxRequests: 20 }, // 20 accepts per minute
};

// Singleton instance
const rateLimitService = new RateLimitService();

export default rateLimitService;

/**
 * Utility function to get client identifier for rate limiting
 * Uses user ID if authenticated, otherwise falls back to IP
 */
export function getClientIdentifier(request: Request, userId?: string): string {
    if (userId) {
        return `user:${userId}`;
    }
    
    // Try to get IP from various headers
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    
    const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
    return `ip:${ip}`;
}

/**
 * Create rate limit response with appropriate headers
 */
export function createRateLimitResponse(
    remaining: number,
    resetTime: number,
    message = 'Too many requests'
): Response {
    return new Response(JSON.stringify({ error: message }), {
        status: 429,
        headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Remaining': remaining.toString(),
            'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),
            'Retry-After': Math.ceil((resetTime - Date.now()) / 1000).toString()
        }
    });
}
