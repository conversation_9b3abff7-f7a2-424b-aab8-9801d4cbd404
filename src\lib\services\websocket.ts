import { browser } from '$app/environment';
import { auth } from '$lib/stores/auth';
import { get } from 'svelte/store';
import { page } from '$app/stores';

export interface SSEMessage {
	type: string;
	[key: string]: any;
}

export interface ChatMessage {
	_id: string;
	message: string;
	sender: string;
	senderProfilePicture?: string;
	receiver?: string;
	group?: string;
	timestamp: string;
	attachments?: any[];
}

class RealtimeService {
	private eventSource: EventSource | null = null;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectDelay = 1000; // Start with 1 second
	private isConnecting = false;
	private messageHandlers = new Map<string, ((data: any) => void)[]>();
	private currentChatId: string | null = null;
	private currentChatType: 'user' | 'group' | null = null;
	private authUnsubscribe: (() => void) | null = null;
	private pageUnsubscribe: (() => void) | null = null;
	private shouldAutoConnect = false; // Track if we should auto-connect

	constructor() {
		// Set up auth and page subscriptions for route-aware connection management
		if (browser) {
			this.setupAuthSubscription();
			this.setupPageSubscription();
		}
	}

	private setupAuthSubscription() {
		// Subscribe to auth changes and auto-connect when authenticated and on chat page
		this.authUnsubscribe = auth.subscribe((authState) => {
			if (authState.isInitialized && authState.token && authState.username) {
				// Only connect if we should auto-connect (on chat page) and not already connected
				if (this.shouldAutoConnect && !this.isConnected && !this.isConnecting) {
					console.log('WebSocket: Auth available and on chat page, auto-connecting...');
					this.connect(authState.token);
				}
			} else if (authState.isInitialized && !authState.token) {
				// User logged out, disconnect
				this.disconnect();
			}
		});
	}

	private setupPageSubscription() {
		// Subscribe to page changes to manage connection based on current route
		this.pageUnsubscribe = page.subscribe((pageState) => {
			const isOnChatPage = pageState.route?.id === '/chat' ||
								 pageState.url?.pathname === '/chat' ||
								 pageState.url?.pathname?.startsWith('/chat');

			console.log('WebSocket: Page changed:', {
				routeId: pageState.route?.id,
				pathname: pageState.url?.pathname,
				isOnChatPage,
				shouldAutoConnect: this.shouldAutoConnect
			});

			if (isOnChatPage && !this.shouldAutoConnect) {
				// Navigated to chat page - enable auto-connect and connect if authenticated
				console.log('WebSocket: Navigated to chat page, enabling auto-connect');
				this.shouldAutoConnect = true;

				const authState = get(auth);
				if (authState.token && authState.username && !this.isConnected && !this.isConnecting) {
					console.log('WebSocket: Auto-connecting on chat page navigation');
					this.connect(authState.token);
				}
			} else if (!isOnChatPage && this.shouldAutoConnect) {
				// Navigated away from chat page - disable auto-connect and disconnect
				console.log('WebSocket: Navigated away from chat page, disconnecting');
				this.shouldAutoConnect = false;
				this.disconnect();
			}
		});
	}

	connect(token?: string) {
		if (!browser) {
			console.log('WebSocket: Not in browser environment');
			return;
		}

		// If already connected and working, don't reconnect
		if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
			console.log('WebSocket: Already connected');
			return;
		}

		// If currently connecting, don't start another connection
		if (this.isConnecting) {
			console.log('WebSocket: Already connecting');
			return;
		}

		// Get token from auth store if not provided
		if (!token) {
			const authState = get(auth);
			token = authState.token;
			if (!token) {
				console.error('WebSocket: No token available for connection');
				return;
			}
		}

		console.log('WebSocket: Using token (first 20 chars):', token.substring(0, 20) + '...');

		this.isConnecting = true;
		console.log('WebSocket: Starting connection...');

		try {
			// Close any existing connection first
			if (this.eventSource) {
				this.eventSource.close();
				this.eventSource = null;
			}

			// Create SSE connection with token as query parameter
			const sseUrl = `/api/ws?token=${encodeURIComponent(token)}`;

			console.log('WebSocket: Connecting to SSE (URL length):', sseUrl.length);
			this.eventSource = new EventSource(sseUrl);

			this.eventSource.onopen = () => {
				console.log('WebSocket: SSE connected successfully');
				this.isConnecting = false;
				this.reconnectAttempts = 0;
				this.reconnectDelay = 1000;
			};

			this.eventSource.onmessage = (event) => {
				try {
					const message: SSEMessage = JSON.parse(event.data);
					this.handleMessage(message);
				} catch (error) {
					console.error('WebSocket: Error parsing SSE message:', error);
				}
			};

			this.eventSource.onerror = (error) => {
				console.error('WebSocket: SSE error:', error);
				this.isConnecting = false;

				// Only attempt reconnection if the connection is actually closed
				// EventSource automatically reconnects for network errors, but not for auth errors
				if (this.eventSource?.readyState === EventSource.CLOSED) {
					this.eventSource = null;
					if (this.reconnectAttempts < this.maxReconnectAttempts) {
						console.log(`WebSocket: Connection closed, scheduling reconnect attempt ${this.reconnectAttempts + 1}`);
						this.scheduleReconnect(token);
					} else {
						console.error('WebSocket: Max reconnection attempts reached, giving up');
					}
				} else {
					// For other errors, just log them - EventSource will handle reconnection
					console.log('WebSocket: Non-fatal error, EventSource will handle reconnection');
				}
			};

		} catch (error) {
			console.error('Error creating SSE connection:', error);
			this.isConnecting = false;
		}
	}

	private scheduleReconnect(token: string) {
		this.reconnectAttempts++;

		// Limit reconnection attempts to prevent infinite loops
		if (this.reconnectAttempts > this.maxReconnectAttempts) {
			console.log('WebSocket: Max reconnection attempts reached, stopping');
			return;
		}

		// Use shorter delays for faster reconnection, especially on first few attempts
		const baseDelay = this.reconnectAttempts <= 2 ? 500 : this.reconnectDelay;
		const delay = Math.min(baseDelay * this.reconnectAttempts, 5000); // Cap at 5 seconds

		console.log(`WebSocket: Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

		setTimeout(() => {
			if (this.reconnectAttempts <= this.maxReconnectAttempts) {
				this.connect(token);
			}
		}, delay);
	}

	disconnect() {
		console.log('WebSocket: Disconnecting service');

		// Clear all reconnection attempts
		this.reconnectAttempts = this.maxReconnectAttempts + 1;

		if (this.eventSource) {
			// Remove all event listeners to prevent memory leaks
			this.eventSource.onopen = null;
			this.eventSource.onmessage = null;
			this.eventSource.onerror = null;

			this.eventSource.close();
			this.eventSource = null;
			console.log('WebSocket: EventSource closed and cleared');
		}

		this.isConnecting = false;
		this.reconnectAttempts = 0;
		this.currentChatId = null;
		this.currentChatType = null;

		// Clear auth subscription if exists
		if (this.authUnsubscribe) {
			this.authUnsubscribe();
			this.authUnsubscribe = null;
		}

		// Clear page subscription if exists
		if (this.pageUnsubscribe) {
			this.pageUnsubscribe();
			this.pageUnsubscribe = null;
		}

		// Clear all message handlers to prevent memory leaks
		this.messageHandlers.clear();
		console.log('WebSocket: All handlers and subscriptions cleared');
	}

	private handleMessage(message: SSEMessage) {
		console.log('Received SSE message:', message);

		// Call registered handlers for this message type
		const handlers = this.messageHandlers.get(message.type) || [];
		handlers.forEach(handler => {
			try {
				handler(message);
			} catch (error) {
				console.error('Error in message handler:', error);
			}
		});
	}

	// Register a handler for a specific message type
	on(messageType: string, handler: (data: any) => void) {
		if (!this.messageHandlers.has(messageType)) {
			this.messageHandlers.set(messageType, []);
		}
		this.messageHandlers.get(messageType)!.push(handler);

		// Return unsubscribe function
		return () => {
			const handlers = this.messageHandlers.get(messageType);
			if (handlers) {
				const index = handlers.indexOf(handler);
				if (index > -1) {
					handlers.splice(index, 1);
				}
			}
		};
	}

	// Join a specific chat (user or group)
	joinChat(chatId: string, chatType: 'user' | 'group') {
		this.currentChatId = chatId;
		this.currentChatType = chatType;
		// SSE doesn't need to send join messages - server broadcasts to all relevant users
	}

	// Leave current chat
	leaveChat() {
		this.currentChatId = null;
		this.currentChatType = null;
		// SSE doesn't need to send leave messages
	}

	// Force reconnection (useful for refresh scenarios)
	forceReconnect() {
		console.log('WebSocket: Force reconnecting...');
		this.disconnect();
		this.reconnectAttempts = 0; // Reset attempts

		// Get token and reconnect
		const authState = get(auth);
		if (authState.token) {
			setTimeout(() => this.connect(authState.token), 100);
		}
	}

	// Enable chat connection (called when entering chat page)
	enableChatConnection() {
		console.log('WebSocket: Enabling chat connection');
		this.shouldAutoConnect = true;

		// Connect immediately if authenticated and not already connected
		const authState = get(auth);
		if (authState.token && authState.username && !this.isConnected && !this.isConnecting) {
			console.log('WebSocket: Auto-connecting after enabling chat connection');
			this.connect(authState.token);
		}
	}

	// Ensure connection is active (useful for manual checks)
	ensureConnection() {
		if (!this.shouldAutoConnect) {
			console.log('WebSocket: Cannot ensure connection - auto-connect disabled');
			return;
		}

		const authState = get(auth);
		if (!authState.token || !authState.username) {
			console.log('WebSocket: Cannot ensure connection - no auth');
			return;
		}

		if (this.isConnected) {
			console.log('WebSocket: Connection already active');
			return;
		}

		if (this.isConnecting) {
			console.log('WebSocket: Already connecting');
			return;
		}

		console.log('WebSocket: Ensuring connection - reconnecting');
		this.connect(authState.token);
	}

	// Disable chat connection (called when leaving chat page)
	disableChatConnection() {
		console.log('WebSocket: Disabling chat connection');
		this.shouldAutoConnect = false;
		this.disconnect();
	}

	// Get connection status
	get isConnected() {
		return this.eventSource && this.eventSource.readyState === EventSource.OPEN;
	}

	get connectionState() {
		if (!this.eventSource) return 'disconnected';
		if (this.isConnecting) return 'connecting';

		switch (this.eventSource.readyState) {
			case EventSource.CONNECTING: return 'connecting';
			case EventSource.OPEN: return 'connected';
			case EventSource.CLOSED: return 'disconnected';
			default: return 'unknown';
		}
	}
}

// Create singleton instance
export const websocketService = new RealtimeService();
