<script>
	import { onMount } from 'svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';

	let loaded = false;

	onMount(() => {
		loaded = true;
	});
</script>

<PageTransition>
	<div class="min-h-screen bg-slate-50">
	<div class="container mx-auto px-4 py-8">
		<nav class="mb-8 flex items-center justify-between">
			<a href="/" class="font-mono text-xl text-black">konekt</a>
			<div class="flex items-center space-x-6">
				<a href="/" class="font-mono text-sm text-black hover:text-gray-600">home</a>
				<a href="/login" class="font-mono text-sm text-black hover:text-gray-600">login</a>
				<a href="/register" class="font-mono text-sm text-black hover:text-gray-600">register</a>
			</div>
		</nav>

		<div class="flex flex-col items-center justify-center space-y-8">
			<div
				class={`transform transition-all duration-1000 ${loaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
			>
				<h1 class="text-center font-mono text-5xl text-black">konekt</h1>
				<p class="mt-2 text-center text-xl text-gray-600">simple, secure messaging</p>
				<p class="mt-4 max-w-xl text-center text-sm text-gray-500">
					seamless encryption. clean design. powerful features.
				</p>
			</div>

			<div
				class={`flex transform flex-col gap-4 transition-all delay-300 duration-1000 sm:flex-row ${loaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
			>
				<a
					class="rounded-md bg-black px-6 py-2 font-mono text-sm text-white transition-all hover:bg-gray-800"
					href="/login"
				>
					start chatting
				</a>
			</div>

			<div
				class={`w-full max-w-3xl transform rounded-lg bg-white shadow-lg transition-all delay-500 duration-1000 ${loaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
			>
				<div class="p-6">
					<div class="mb-6 flex items-center justify-between">
						<div class="flex items-center space-x-2">
							<div class="h-2 w-2 rounded-full bg-black"></div>
							<div class="h-2 w-2 rounded-full bg-black"></div>
							<div class="h-2 w-2 rounded-full bg-black"></div>
						</div>
						<p class="font-mono text-xs text-gray-400">encrypted</p>
					</div>
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="h-8 w-8 flex-shrink-0 rounded-full bg-black"></div>
							<div class="max-w-sm rounded-lg bg-gray-50 p-3">
								<p class="font-mono text-sm text-black">welcome to konekt ✨</p>
								<p class="mt-1 text-xs text-gray-400">10:45</p>
							</div>
						</div>
						<div class="flex items-start justify-end space-x-3">
							<div class="max-w-sm rounded-lg bg-black p-3">
								<p class="font-mono text-sm text-white">the best messenger yet.</p>
								<p class="mt-1 text-xs text-gray-500">10:46</p>
							</div>
							<div class="h-8 w-8 flex-shrink-0 rounded-full bg-black"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
				<div class="text-center">
					<div class="mb-3 inline-block rounded-full bg-gray-50 p-3">
						<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
							></path>
						</svg>
					</div>
					<h3 class="font-mono text-lg text-black">secure by design</h3>
					<p class="mt-2 text-sm text-gray-500">encrypted end-to-end, always</p>
				</div>
				<div class="text-center">
					<div class="mb-3 inline-block rounded-full bg-gray-50 p-3">
						<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 10V3L4 14h7v7l9-11h-7z"
							></path>
						</svg>
					</div>
					<h3 class="font-mono text-lg text-black">lightning fast</h3>
					<p class="mt-2 text-sm text-gray-500">instant delivery everywhere</p>
				</div>
				<div class="text-center">
					<div class="mb-3 inline-block rounded-full bg-gray-50 p-3">
						<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
							></path>
						</svg>
					</div>
					<h3 class="font-mono text-lg text-black">team friendly</h3>
					<p class="mt-2 text-sm text-gray-500">perfect for any size team</p>
				</div>
			</div>
		</div>
	</div>
</div>
</PageTransition>
