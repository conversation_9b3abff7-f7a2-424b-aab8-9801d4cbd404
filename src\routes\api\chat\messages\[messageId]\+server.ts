import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Chat from '$lib/models/Chat';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import { ENV } from '$lib/config/env';
import r2Storage from '$lib/services/r2Storage';

export const DELETE: RequestHandler = async ({ params, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	const { messageId } = params;
	if (!messageId || !mongoose.Types.ObjectId.isValid(messageId)) {
		return json({ error: 'Invalid message ID' }, { status: 400 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const userId = decoded.userId;

		// Find the message and ensure the user deleting it is the sender
		const message = await Chat.findOne({ _id: messageId, sender: userId });

		if (!message) {
			// Either message doesn't exist or user is not the sender
			return json({ error: 'Message not found or permission denied' }, { status: 404 });
		}

		// Delete attachments from R2 storage before deleting the message
		if (message.attachments && message.attachments.length > 0) {
			console.log(`Deleting ${message.attachments.length} attachments for message ${messageId}`);

			for (const attachment of message.attachments) {
				if (attachment.key) {
					try {
						await r2Storage.deleteFile(attachment.key);
						console.log(`Deleted attachment: ${attachment.key}`);
					} catch (deleteError) {
						console.warn(`Failed to delete attachment ${attachment.key}:`, deleteError);
						// Continue with other attachments even if one fails
					}
				}
			}
		}

		// Proceed with deletion
		await Chat.deleteOne({ _id: messageId });

		// Return success, potentially including messageId and chat target (user/group)
		// for WebSocket broadcasting coordination from the client side.
		return json({
			success: true,
			deletedMessageId: messageId,
			conversationTarget: message.receiver?.toString() || message.group?.toString(), // Helps client know which chat to update
			isGroup: !!message.group
		});
	} catch (err) {
		console.error('Error deleting message:', err);
		if (err instanceof jwt.JsonWebTokenError) {
			return json({ error: 'Invalid token' }, { status: 401 });
		}
		return json({ error: 'Failed to delete message' }, { status: 500 });
	}
};
