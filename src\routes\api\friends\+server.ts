import type { Re<PERSON><PERSON>and<PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Friend from '$lib/models/Friend';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';
import { withRateLimit } from '$lib/middleware/rateLimit';
import { RATE_LIMITS } from '$lib/services/rateLimit';
import { broadcastToUsers } from '$lib/services/realtime';

// Get friends, pending requests, and sent requests
const getFriends: RequestHandler = async ({ cookies }) => {
    const token = cookies.get('token');
    if (!token) {
        return json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        await connect();
        const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
        const userId = decoded.userId;

        // Get all friends
        const friends = await Friend.getFriends(userId);

        // Get pending friend requests (received)
        const pendingRequests = await Friend.getPendingRequests(userId);

        // Get sent friend requests
        const sentRequests = await Friend.getSentRequests(userId);

        return json({
            friends: friends.map((friend: any) => ({
                _id: friend._id,
                username: friend.username,
                profilePicture: friend.profilePicture
            })),
            pendingRequests: pendingRequests.map((req: any) => ({
                _id: req._id,
                requester: {
                    _id: req.requester._id,
                    username: req.requester.username,
                    profilePicture: req.requester.profilePicture
                },
                createdAt: req.createdAt
            })),
            sentRequests: sentRequests.map((req: any) => ({
                _id: req._id,
                recipient: {
                    _id: req.recipient._id,
                    username: req.recipient.username,
                    profilePicture: req.recipient.profilePicture
                },
                createdAt: req.createdAt
            }))
        });
    } catch (err) {
        console.error('Error fetching friends:', err);
        if (err instanceof jwt.JsonWebTokenError) {
            return json({ error: 'Invalid token' }, { status: 401 });
        }
        return json({ error: 'Failed to fetch friends' }, { status: 500 });
    }
};

// Send friend request
const sendFriendRequest: RequestHandler = async ({ request, cookies }) => {
    const token = cookies.get('token');
    if (!token) {
        return json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        await connect();
        const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
        const userId = decoded.userId;

        const { username } = await request.json();

        if (!username) {
            return json({ error: 'Username is required' }, { status: 400 });
        }

        // Find the target user
        const targetUser = await User.findOne({ username });
        if (!targetUser) {
            return json({ error: 'User not found' }, { status: 404 });
        }

        // Check if trying to friend themselves
        if (targetUser._id.toString() === userId) {
            return json({ error: 'Cannot send friend request to yourself' }, { status: 400 });
        }

        // Check if friendship already exists
        const existingFriendship = await Friend.getFriendship(userId, targetUser._id.toString());
        if (existingFriendship) {
            if (existingFriendship.status === 'accepted') {
                return json({ error: 'Already friends' }, { status: 400 });
            } else if (existingFriendship.status === 'pending') {
                return json({ error: 'Friend request already sent' }, { status: 400 });
            } else if (existingFriendship.status === 'blocked') {
                return json({ error: 'Cannot send friend request' }, { status: 400 });
            }
        }

        // Create friend request
        const friendRequest = new Friend({
            requester: userId,
            recipient: targetUser._id,
            status: 'pending'
        });

        await friendRequest.save();

        // Broadcast real-time notification to the recipient
        try {
            const senderUser = await User.findById(userId);
            broadcastToUsers([targetUser.username], {
                type: 'friend_request_received',
                friendRequest: {
                    _id: friendRequest._id,
                    requester: {
                        _id: senderUser._id,
                        username: senderUser.username,
                        profilePicture: senderUser.profilePicture
                    },
                    createdAt: friendRequest.createdAt
                }
            });
        } catch (broadcastError) {
            console.error('Error broadcasting friend request:', broadcastError);
            // Don't fail the request if broadcast fails
        }

        return json({
            success: true,
            message: `Friend request sent to ${username}`
        });
    } catch (err) {
        console.error('Error sending friend request:', err);
        if (err instanceof jwt.JsonWebTokenError) {
            return json({ error: 'Invalid token' }, { status: 401 });
        }
        return json({ error: 'Failed to send friend request' }, { status: 500 });
    }
};

export const GET = withRateLimit(RATE_LIMITS.GENERAL, getFriends);
export const POST = withRateLimit(RATE_LIMITS.SEND_FRIEND_REQUEST, sendFriendRequest);
