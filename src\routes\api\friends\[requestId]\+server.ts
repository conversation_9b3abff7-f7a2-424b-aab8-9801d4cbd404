import type { Request<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Friend from '$lib/models/Friend';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import { ENV } from '$lib/config/env';
import { withRateLimit } from '$lib/middleware/rateLimit';
import { RATE_LIMITS } from '$lib/services/rateLimit';
import { broadcastToUsers } from '$lib/services/realtime';

// Accept or decline friend request
const manageFriendRequest: RequestHandler = async ({ params, request, cookies }) => {
    const token = cookies.get('token');
    if (!token) {
        return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { requestId } = params;
    if (!requestId || !mongoose.Types.ObjectId.isValid(requestId)) {
        return json({ error: 'Invalid request ID' }, { status: 400 });
    }

    try {
        await connect();
        const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
        const userId = decoded.userId;

        const { action } = await request.json();

        if (!action || !['accept', 'decline'].includes(action)) {
            return json({ error: 'Invalid action. Must be "accept" or "decline"' }, { status: 400 });
        }

        // Find the friend request
        const friendRequest = await Friend.findById(requestId);
        if (!friendRequest) {
            return json({ error: 'Friend request not found' }, { status: 404 });
        }

        // Verify the user is the recipient of the request
        if (friendRequest.recipient.toString() !== userId) {
            return json({ error: 'Not authorized to manage this request' }, { status: 403 });
        }

        // Check if request is still pending
        if (friendRequest.status !== 'pending') {
            return json({ error: 'Friend request is no longer pending' }, { status: 400 });
        }

        // Update the request status
        friendRequest.status = action === 'accept' ? 'accepted' : 'declined';
        await friendRequest.save();

        // Broadcast real-time notification to the requester
        try {
            const requesterUser = await User.findById(friendRequest.requester);
            const recipientUser = await User.findById(friendRequest.recipient);

            if (requesterUser && recipientUser) {
                // Notify the requester about the response
                broadcastToUsers([requesterUser.username], {
                    type: 'friend_request_response',
                    action: action,
                    friendRequest: {
                        _id: friendRequest._id,
                        recipient: {
                            _id: recipientUser._id,
                            username: recipientUser.username,
                            profilePicture: recipientUser.profilePicture
                        },
                        status: friendRequest.status
                    }
                });

                // If accepted, also notify both users about the new friendship
                if (action === 'accept') {
                    broadcastToUsers([requesterUser.username, recipientUser.username], {
                        type: 'friendship_established',
                        friend: {
                            _id: action === 'accept' ? recipientUser._id : requesterUser._id,
                            username: action === 'accept' ? recipientUser.username : requesterUser.username,
                            profilePicture: action === 'accept' ? recipientUser.profilePicture : requesterUser.profilePicture
                        }
                    });
                }
            }
        } catch (broadcastError) {
            console.error('Error broadcasting friend request response:', broadcastError);
            // Don't fail the request if broadcast fails
        }

        const message = action === 'accept'
            ? 'Friend request accepted'
            : 'Friend request declined';

        return json({
            success: true,
            message,
            status: friendRequest.status
        });
    } catch (err) {
        console.error('Error managing friend request:', err);
        if (err instanceof jwt.JsonWebTokenError) {
            return json({ error: 'Invalid token' }, { status: 401 });
        }
        return json({ error: 'Failed to manage friend request' }, { status: 500 });
    }
};

// Remove friend or cancel friend request
const removeFriend: RequestHandler = async ({ params, cookies }) => {
    const token = cookies.get('token');
    if (!token) {
        return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { requestId } = params;
    if (!requestId || !mongoose.Types.ObjectId.isValid(requestId)) {
        return json({ error: 'Invalid request ID' }, { status: 400 });
    }

    try {
        await connect();
        const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
        const userId = decoded.userId;

        // Find the friendship
        const friendship = await Friend.findById(requestId);
        if (!friendship) {
            return json({ error: 'Friendship not found' }, { status: 404 });
        }

        // Verify the user is part of this friendship
        const isRequester = friendship.requester.toString() === userId;
        const isRecipient = friendship.recipient.toString() === userId;

        if (!isRequester && !isRecipient) {
            return json({ error: 'Not authorized to remove this friendship' }, { status: 403 });
        }

        // Get user information before deleting
        const requesterUser = await User.findById(friendship.requester);
        const recipientUser = await User.findById(friendship.recipient);

        // Delete the friendship
        await Friend.findByIdAndDelete(requestId);

        // Broadcast real-time notification to both users
        try {
            if (requesterUser && recipientUser) {
                // Send personalized message to the requester (they removed the recipient)
                broadcastToUsers([requesterUser.username], {
                    type: 'friendship_removed',
                    removedFriend: {
                        _id: recipientUser._id,
                        username: recipientUser.username
                    }
                });

                // Send personalized message to the recipient (they were removed by the requester)
                broadcastToUsers([recipientUser.username], {
                    type: 'friendship_removed',
                    removedFriend: {
                        _id: requesterUser._id,
                        username: requesterUser.username
                    }
                });
            }
        } catch (broadcastError) {
            console.error('Error broadcasting friendship removal:', broadcastError);
            // Don't fail the request if broadcast fails
        }

        return json({
            success: true,
            message: 'Friendship removed successfully'
        });
    } catch (err) {
        console.error('Error removing friend:', err);
        if (err instanceof jwt.JsonWebTokenError) {
            return json({ error: 'Invalid token' }, { status: 401 });
        }
        return json({ error: 'Failed to remove friend' }, { status: 500 });
    }
};

export const PUT = withRateLimit(RATE_LIMITS.ACCEPT_FRIEND_REQUEST, manageFriendRequest);
export const DELETE = withRateLimit(RATE_LIMITS.GENERAL, removeFriend);
