import type { RequestHand<PERSON> } from '@sveltejs/kit';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import r2Storage from '$lib/services/r2Storage';

export const GET: RequestHandler = async ({ params }) => {
	const { key } = params;

	if (!key) {
		return new Response('Avatar key required', { status: 400 });
	}

	try {
		await connect();

		// Verify that this key belongs to a user's profile picture
		// This prevents unauthorized access to random files
		const user = await User.findOne({ profilePictureKey: key });

		if (!user) {
			return new Response('Avatar not found', { status: 404 });
		}

		// Get file from R2
		const fileData = await r2Storage.getFile(key);

		if (!fileData) {
			return new Response('Avatar file not found in storage', { status: 404 });
		}

		// Determine content type based on file extension
		const extension = key.split('.').pop()?.toLowerCase();
		let contentType = 'image/jpeg'; // Default

		switch (extension) {
			case 'png':
				contentType = 'image/png';
				break;
			case 'gif':
				contentType = 'image/gif';
				break;
			case 'webp':
				contentType = 'image/webp';
				break;
			case 'jpg':
			case 'jpeg':
			default:
				contentType = 'image/jpeg';
				break;
		}

		return new Response(fileData, {
			headers: {
				'Content-Type': contentType,
				'Content-Disposition': 'inline',
				'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
				'Access-Control-Allow-Origin': '*', // Allow cross-origin requests for avatars
			}
		});

	} catch (err) {
		console.error('Error serving avatar:', err);
		return new Response('Internal server error', { status: 500 });
	}
};
