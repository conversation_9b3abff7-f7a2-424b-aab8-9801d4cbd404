import type { Request<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import r2Storage from '$lib/services/r2Storage';
import { connect } from '$lib/db';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const user = await User.findById(decoded.userId);

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		return json({
			username: user.username,
			profilePicture: user.profilePicture
		});
	} catch (err) {
		console.error('Error fetching profile:', err);
		return json({ error: 'Failed to fetch profile' }, { status: 500 });
	}
};

export const PUT: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const formData = await request.formData();
		const updates: any = {};

		// Get current user to handle old profile picture deletion
		const currentUser = await User.findById(decoded.userId);
		if (!currentUser) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		if (formData.has('username')) {
			const newUsername = formData.get('username') as string;
			if (newUsername && newUsername.trim()) {
				updates.username = newUsername.trim();
			}
		}

		// Handle profile picture key update (from upload API)
		if (formData.has('profilePictureKey')) {
			const key = formData.get('profilePictureKey') as string;
			if (key) {
				// Delete old profile picture if exists
				if (currentUser.profilePictureKey) {
					try {
						await r2Storage.deleteFile(currentUser.profilePictureKey);
					} catch (deleteError) {
						console.warn('Failed to delete old profile picture:', deleteError);
					}
				}
				updates.profilePictureKey = key;
			}
		}

		// Handle profile picture removal
		if (formData.get('removeProfilePicture') === 'true') {
			if (currentUser.profilePictureKey) {
				try {
					await r2Storage.deleteFile(currentUser.profilePictureKey);
				} catch (deleteError) {
					console.warn('Failed to delete profile picture:', deleteError);
				}
			}
			updates.profilePictureKey = null;
		}

		const user = await User.findByIdAndUpdate(decoded.userId, updates, { new: true });

		return json({
			success: true,
			user: {
				username: user?.username,
				profilePicture: user?.profilePicture
			}
		});
	} catch (err) {
		console.error('Error updating profile:', err);
		return json({ error: 'Failed to update profile' }, { status: 500 });
	}
};
