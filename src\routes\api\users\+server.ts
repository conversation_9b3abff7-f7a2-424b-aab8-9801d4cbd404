import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import Friend from '$lib/models/Friend';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';
import { getTokenFromRequest } from '$lib/utils/auth';
import { withRateLimit } from '$lib/middleware/rateLimit';
import { RATE_LIMITS } from '$lib/services/rateLimit';

const getUsers: RequestHandler = async (event) => {
	const token = getTokenFromRequest(event);
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		// Decode token first (no DB connection needed)
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);

		// Connect to database only when needed
		await connect();
		const currentUser = await User.findById(decoded.userId).lean();

		if (!currentUser) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// Get only friends instead of all users
		const friends = await Friend.getFriends(decoded.userId);

		// Extract usernames from friends
		const friendUsernames = friends.map((friend: any) => friend.username);

		return json({ users: friendUsernames });
	} catch (err) {
		console.error('Error fetching users:', err);
		return json({ error: 'Failed to fetch users' }, { status: 500 });
	}
};

export const GET = withRateLimit(RATE_LIMITS.GENERAL, getUsers);
