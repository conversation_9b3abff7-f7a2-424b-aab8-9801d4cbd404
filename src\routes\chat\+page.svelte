<script lang="ts">
	import { onMount, onDestroy, tick } from 'svelte'; // Import tick
	import { fade } from 'svelte/transition';
	import { get } from 'svelte/store';
	import { auth } from '$lib/stores/auth';
	import { formatDistanceToNow } from 'date-fns'; // Import date-fns directly
	import FileUpload from '$lib/components/FileUpload.svelte';
	import AttachmentDisplay from '$lib/components/AttachmentDisplay.svelte';
	import type { IAttachment } from '$lib/models/Chat';
	import { websocketService } from '$lib/services/websocket';
	import { authenticatedFetch } from '$lib/utils/api';
	import { performLogout } from '$lib/utils/logout';
	import ConfirmDialog from '$lib/components/ConfirmDialog.svelte';
	import FriendsPanel from '$lib/components/FriendsPanel.svelte';
	import SettingsPanel from '$lib/components/SettingsPanel.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';

	// --- State Variables ---
	let currentUsername: string = '';
	let users: string[] = [];
	let groups: {
		_id: string;
		name: string;
		members: { username: string }[];
		createdBy: { username: string };
	}[] = [];
	let selectedUser: string | null = null;
	let selectedGroup: string | null = null;
	let messages: { _id: string; sender: string; senderProfilePicture?: string; message: string; attachments?: IAttachment[]; timestamp: Date }[] = [];
	let newMessage = '';
	let messagePollingInterval: NodeJS.Timeout | undefined;
	let isCreatingGroup = false;
	let newGroupName = '';
	let selectedMembers: string[] = [];
	let isDirectMessages = true;

	// Loading states
	let loadingUsers = true;
	let loadingGroups = true;
	let loadingMessages = false;
	let initialLoadComplete = false; // Track if initial user/group load is done

	// UI State
	let errorMessage = ''; // For displaying errors to the user
	let successMessage = ''; // For displaying success messages
	let selectedFiles: File[] = [];
	let uploadingFiles = false;
	let connectionStatus = 'connecting'; // Track WebSocket connection status

	// Confirm dialog state
	let showDeleteMessageDialog = false;
	let messageToDelete: string | null = null;
	let deletingMessage = false;

	// Panel state
	let showFriendsPanel = false;
	let showSettingsPanel = false;

	// --- THIS IS THE CORRECT DECLARATION ---
	let currentUserProfile = {
		username: '',
		profilePicture: null as string | null
	};

	// Reactive statement to immediately sync currentUsername with auth store
	$: {
		const authState = get(auth);
		if (authState.username && authState.username !== currentUsername) {
			console.log('Chat: Reactive sync - updating currentUsername from', currentUsername, 'to', authState.username);
			currentUsername = authState.username;
			currentUserProfile.username = currentUsername;
			// Force re-render of messages when username changes
			messages = [...messages];
		}
	}

	// --- Authentication & Initialization ---
	let authUnsubscribe: (() => void) | null = null;

	// Re-enable auth subscription with proper handling
	if (!authUnsubscribe) {
		authUnsubscribe = auth.subscribe(async (authState) => {
			const previousUsername = currentUsername;
			currentUsername = authState.username || '';
			currentUserProfile.username = currentUsername;

			console.log('Chat: Auth state changed - currentUsername:', currentUsername, 'previous:', previousUsername);

			// Force reactivity update when username changes
			if (currentUsername && currentUsername !== previousUsername) {
				await fetchCurrentUserProfile();
				// Force re-render of messages by reassigning the array
				messages = [...messages];
			}

			// Only redirect if clearly not authenticated and initialized
			if (authState.isInitialized && !authState.username && !authState.token) {
				console.log('Chat: No auth detected, redirecting to login');
				handleLogout();
			}
		});
	}

	// Helper function for timestamp formatting (if not imported)
	function formatTimestamp(timestamp: Date | string): string {
		try {
			const date = new Date(timestamp);
			const now = new Date();
			const diffInSeconds = (now.getTime() - date.getTime()) / 1000;

			if (diffInSeconds < 5) return 'just now';
			if (diffInSeconds < 60) return `${Math.floor(diffInSeconds)}s ago`;
			// Use formatDistanceToNow for times within the last day or so
			if (diffInSeconds < 60 * 60 * 24) {
				// Less than a day
				return formatDistanceToNow(date, { addSuffix: true });
			} else {
				// Format older dates more specifically
				return (
					date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' }) +
					' ' +
					date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })
				);
			}
		} catch (e) {
			console.error('Error formatting timestamp:', e);
			return new Date(timestamp).toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }); // Fallback
		}
	}




	// --- Validate Session ---
	async function validateSession() {
		try {
			const res = await authenticatedFetch('/api/auth/validate');
			if (!res.ok) {
				handleLogout(); // Use dedicated logout function
			} else {
				const data = await res.json();
				currentUsername = data.username; // Set current username immediately
				console.log('Chat: Session validated, currentUsername set to:', currentUsername);

				// Update auth store with validated info
				const token = localStorage.getItem('auth_token');
				if (token) {
					auth.setAuth(token, data.username);
				}

				await Promise.all([fetchUsers(), fetchGroups(), fetchCurrentUserProfile()]); // Fetch initial data including profile
				initialLoadComplete = true;
			}
		} catch (err) {
			console.error('Error validating session:', err);
			handleLogout(); // Use dedicated logout function
		}
	}

	// --- Panel Management ---
	function openFriendsPanel() {
		showFriendsPanel = true;
		showSettingsPanel = false;
	}

	function openSettingsPanel() {
		showSettingsPanel = true;
		showFriendsPanel = false;
	}

	function closePanels() {
		showFriendsPanel = false;
		showSettingsPanel = false;
	}

	// --- Logout Handling ---
	async function handleLogout() {
		// Stop message polling before logout
		stopMessagePolling();
		// Use shared logout utility
		await performLogout();
	}


	// --- Data Fetching ---
	async function fetchCurrentUserProfile() {
		try {
			const res = await authenticatedFetch('/api/user/profile');
			if (res.ok) {
				const profile = await res.json();
				currentUserProfile = {
					username: profile.username || currentUsername,
					profilePicture: profile.profilePicture || null
				};
			} else {
				console.error('Failed to fetch user profile');
			}
		} catch (err) {
			console.error('Error fetching user profile:', err);
		}
	}

	async function fetchUsers() {
		loadingUsers = true;
		try {
			const res = await authenticatedFetch('/api/users');
			if (res.ok) {
				const data = await res.json();
				users = (data.users || []).filter((user: string) => user !== currentUsername);
			} else {
				handleApiError('Failed to fetch users', await res.text());
				users = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching users', err.message);
			users = [];
		} finally {
			loadingUsers = false;
		}
	}

	async function fetchMessages() {
		if (!selectedUser) return;
		loadingMessages = true;
		errorMessage = ''; // Clear previous errors
		try {
			const res = await authenticatedFetch(`/api/chat/messages?receiver=${selectedUser}`);
			if (res.ok) {
				const fetchedMessages = await res.json();
				// Ensure timestamp is a Date object and messages have _id
				messages = (fetchedMessages || []).map((m: any) => ({
					...m,
					_id: m._id || `client-${Math.random()}`, // Assign client-side ID if missing (shouldn't happen ideally)
					timestamp: new Date(m.timestamp)
				}));
				await tick(); // Wait for DOM update before scrolling
				scrollToBottom();
			} else {
				handleApiError('Failed to fetch messages', await res.text());
				messages = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching messages', err.message);
			messages = [];
		} finally {
			loadingMessages = false;
		}
	}

	async function fetchGroupMessages() {
		if (!selectedGroup) return;
		loadingMessages = true;
		errorMessage = '';
		try {
			const res = await fetch(`/api/chat/messages?group=${selectedGroup}`, {
				credentials: 'include'
			});
			if (res.ok) {
				const fetchedMessages = await res.json();
				messages = (fetchedMessages || []).map((m: any) => ({
					...m,
					_id: m._id || `client-${Math.random()}`,
					timestamp: new Date(m.timestamp)
				}));
				await tick();
				scrollToBottom();
			} else {
				handleApiError('Failed to fetch group messages', await res.text());
				messages = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching group messages', err.message);
			messages = [];
		} finally {
			loadingMessages = false;
		}
	}

	async function fetchGroups() {
		loadingGroups = true;
		try {
			const res = await fetch('/api/groups', { credentials: 'include' });
			if (res.ok) {
				const data = await res.json();
				groups = data.groups || []; // Ensure it's an array
			} else {
				handleApiError('Failed to fetch groups', await res.text());
				groups = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching groups', err.message);
			groups = [];
		} finally {
			loadingGroups = false;
		}
	}

	// --- Actions (SendMessage, CreateGroup, DeleteMessage, DeleteGroup) ---
	async function sendMessage() {
		if ((!newMessage.trim() && selectedFiles.length === 0) || (!selectedUser && !selectedGroup)) return;

		// Ensure currentUsername is set from auth store before sending
		const authState = get(auth);
		if (!currentUsername && authState.username) {
			currentUsername = authState.username;
			console.log('Chat: Setting currentUsername from auth store before sending:', currentUsername);
		}

		const messageToSend = newMessage.trim();
		const tempId = `temp-${Date.now()}`;
		uploadingFiles = true;

		// Optimistic UI update
		const optimisticMessage = {
			_id: tempId,
			sender: currentUsername,
			message: messageToSend,
			attachments: selectedFiles.map(file => ({
				key: '',
				url: '',
				filename: file.name,
				contentType: file.type,
				size: file.size
			})),
			timestamp: new Date()
		};
		messages = [...messages, optimisticMessage];
		newMessage = ''; // Clear input *after* storing value
		const filesToSend = [...selectedFiles];
		selectedFiles = []; // Clear selected files
		await tick();
		scrollToBottom();

		try {
			let res: Response;

			if (filesToSend.length > 0) {
				// Send with attachments using FormData
				const formData = new FormData();
				if (messageToSend) formData.append('message', messageToSend);
				if (selectedUser) formData.append('receiver', selectedUser);
				if (selectedGroup) formData.append('groupId', selectedGroup);

				filesToSend.forEach(file => {
					formData.append('attachments', file);
				});

				res = await authenticatedFetch('/api/chat/send', {
					method: 'POST',
					body: formData
				});
			} else {
				// Send text-only message using JSON
				const body = selectedUser
					? { receiver: selectedUser, message: messageToSend }
					: { groupId: selectedGroup, message: messageToSend };

				res = await authenticatedFetch('/api/chat/send', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify(body)
				});
			}

			if (!res.ok) {
				handleApiError('Error sending message', await res.text());
				// Remove optimistic message on failure
				messages = messages.filter((m) => m._id !== tempId);
			} else {
				// Message sent successfully - keep optimistic message until WebSocket message arrives
				// The WebSocket handler will remove all temp messages and add the real one
				console.log('Message sent successfully, waiting for WebSocket confirmation');
			}
		} catch (err: any) {
			handleApiError('Error sending message', err.message);
			// Remove optimistic message on network failure
			messages = messages.filter((m) => m._id !== tempId);
		} finally {
			uploadingFiles = false;
		}
	}

	// --- File Upload Handling ---
	function handleFilesSelected(event: CustomEvent<{ files: File[] }>) {
		selectedFiles = [...selectedFiles, ...event.detail.files];
	}

	function handleFileError(event: CustomEvent<{ message: string }>) {
		handleApiError('File Error', event.detail.message);
	}

	function removeSelectedFile(index: number) {
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
	}

	async function createGroup() {
		if (!newGroupName.trim() || selectedMembers.length === 0) {
            handleApiError("Create Group Error", "Group name cannot be empty and must have at least one member.");
            return;
        };

		try {
			const res = await fetch('/api/groups', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({ name: newGroupName.trim(), members: selectedMembers })
			});

			if (res.ok) {
				const data = await res.json();
				isCreatingGroup = false;
				newGroupName = '';
				selectedMembers = [];
				await fetchGroups(); // Refresh group list
				setSuccessMessage('Group created successfully!');
				if (data.group?._id) { // Check if group ID exists
					selectGroup(data.group._id); // Select the new group
				}
            } else {
				handleApiError('Error creating group', await res.text());
			}
		} catch (err: any) {
			handleApiError('Error creating group', err.message);
		}
	}


    function deleteMessage(messageId: string) {
		messageToDelete = messageId;
		showDeleteMessageDialog = true;
	}

	async function confirmDeleteMessage() {
		if (!messageToDelete) return;

		deletingMessage = true;
		const originalMessages = [...messages];
		messages = messages.filter(msg => msg._id !== messageToDelete); // Optimistic UI update

		try {
			const res = await fetch(`/api/chat/messages/${messageToDelete}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			const data = await res.json();

			if (res.ok && data.success) {
				setSuccessMessage('Message deleted.');
			} else {
				handleApiError('Failed to delete message', data.error || 'Server error');
				messages = originalMessages; // Revert optimistic update
			}
		} catch (err: any) {
			handleApiError('Error deleting message', err.message);
			messages = originalMessages; // Revert optimistic update
		} finally {
			deletingMessage = false;
			showDeleteMessageDialog = false;
			messageToDelete = null;
		}
	}

	function cancelDeleteMessage() {
		showDeleteMessageDialog = false;
		messageToDelete = null;
	}

	async function deleteGroup() {
		const groupToDelete = groups.find((g) => g._id === selectedGroup);
		if (!selectedGroup || !groupToDelete || !confirm(`Are you sure you want to delete the group "${groupToDelete.name}"? This will delete all messages and cannot be undone.`)) return;

		const deletedGroupId = selectedGroup; // Store before clearing state

		// Optimistic UI update
		const originalGroups = [...groups];
		groups = groups.filter((g) => g._id !== deletedGroupId);
		selectedGroup = null; // Clear selection
		messages = [];
		stopMessagePolling();
		isDirectMessages = true; // Switch tab

		try {
			const res = await fetch(`/api/groups/${deletedGroupId}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			const data = await res.json();

			if (res.ok && data.success) {
				setSuccessMessage('Group deleted.');
			} else {
				handleApiError('Failed to delete group', data.error || 'Server error');
				groups = originalGroups; // Revert optimistic update
                isDirectMessages = false; // Switch back tab? Complex state restoration needed.
                await fetchGroups(); // Or just refetch
			}
		} catch (err: any) {
			handleApiError('Error deleting group', err.message);
			groups = originalGroups; // Revert optimistic update
            await fetchGroups(); // Refetch
		}
	}


	// --- UI Logic & Event Handlers ---
	function selectUser(username: string) {
		if (username === currentUsername || selectedUser === username) return;
		stopMessagePolling(); // Stop polling for previous chat first
		isDirectMessages = true;
		selectedUser = username;
		selectedGroup = null;
		messages = [];
		fetchMessages(); // Initial fetch
		websocketService.joinChat(username, 'user'); // Join WebSocket chat
	}

	function selectGroup(groupId: string) {
		if (selectedGroup === groupId) return;
		stopMessagePolling(); // Stop polling for previous chat first
		isDirectMessages = false;
		selectedGroup = groupId;
		selectedUser = null;
		messages = [];
		fetchGroupMessages(); // Initial fetch
		websocketService.joinChat(groupId, 'group'); // Join WebSocket chat
	}

	// WebSocket message handlers
	let wsUnsubscribes: (() => void)[] = [];

	function setupWebSocketHandlers() {
		// Clear existing handlers
		wsUnsubscribes.forEach(unsub => unsub());
		wsUnsubscribes = [];

		// Handle new messages
		const unsubNewMessage = websocketService.on('new_message', (data) => {
			const { message: newMsg, chatType, chatId } = data;

			// Only add message if it's for the current chat
			const isCurrentChat = (
				(chatType === 'user' && selectedUser === chatId) ||
				(chatType === 'group' && selectedGroup === chatId)
			);

			if (isCurrentChat && newMsg) {
				// Convert timestamp to Date object
				const messageWithDate = {
					...newMsg,
					timestamp: new Date(newMsg.timestamp)
				};

				// Remove any temporary optimistic messages first (they start with 'temp-')
				// This prevents the sender confusion issue
				messages = messages.filter(m => !m._id.startsWith('temp-'));

				// Add message if it's not already in the list (avoid duplicates)
				const exists = messages.some(m => m._id === messageWithDate._id);
				if (!exists) {
					messages = [...messages, messageWithDate];
					tick().then(() => scrollToBottom());
				}
			}
		});

		// Handle connection success
		const unsubConnected = websocketService.on('connected', (data: any) => {
			console.log('Chat: SSE connected for user:', data.username);
			updateConnectionStatus();
		});

		// Handle friendship removal - update users list
		const unsubFriendshipRemoved = websocketService.on('friendship_removed', (data: any) => {
			console.log('Chat: Friendship removed:', data);
			// Remove the user from the users list
			users = users.filter(user => user !== data.removedFriend.username);

			// If we're currently chatting with this user, clear the selection
			if (selectedUser === data.removedFriend.username) {
				selectedUser = null;
				messages = [];
			}
		});

		// Handle friend requests
		const unsubFriendRequest = websocketService.on('friend_request_received', (data) => {
			console.log('Received friend request in chat:', data);
			setSuccessMessage(`New friend request from ${data.friendRequest.requester.username}`);
			// Refresh users list to show new friends
			fetchUsers();
		});

		const unsubFriendResponse = websocketService.on('friend_request_response', (data) => {
			console.log('Friend request response in chat:', data);
			if (data.action === 'accept') {
				setSuccessMessage(`${data.friendRequest.recipient.username} accepted your friend request!`);
			}
			// Refresh users list to show new friends
			fetchUsers();
		});

		const unsubFriendshipEstablished = websocketService.on('friendship_established', (data) => {
			console.log('Friendship established in chat:', data);
			// Refresh users list to show new friends
			fetchUsers();
		});

		wsUnsubscribes.push(unsubNewMessage, unsubConnected, unsubFriendshipRemoved, unsubFriendRequest, unsubFriendResponse, unsubFriendshipEstablished);
	}

	function stopMessagePolling() {
		console.log('Stopping message polling');

		if (messagePollingInterval) {
			clearInterval(messagePollingInterval);
			messagePollingInterval = undefined;
		}

		// Clear any pending fetch requests
		if (typeof AbortController !== 'undefined') {
			// Note: In a real implementation, you'd want to track AbortControllers
			// and abort them here to cancel pending requests
		}

		websocketService.leaveChat();
	}

	// Simple connection status update
	function updateConnectionStatus() {
		connectionStatus = websocketService.connectionState;

		// If we're disconnected but should be connected, try to ensure connection
		if (connectionStatus === 'disconnected') {
			websocketService.ensureConnection();
		}
	}





	// Error and Success message handling
	function handleApiError(context: string, errorDetails: string) {
		console.error(`${context}:`, errorDetails);
		errorMessage = `${context}: ${String(errorDetails).substring(0, 100)}${
			String(errorDetails).length > 100 ? '...' : ''
		}`;
		successMessage = '';
		setTimeout(() => (errorMessage = ''), 5000); // Clear error after 5s
	}

	function setSuccessMessage(message: string) {
		successMessage = message;
		errorMessage = '';
		setTimeout(() => (successMessage = ''), 3000); // Clear success after 3s
	}



	// --- Lifecycle & DOM ---
	onMount(() => {
		console.log('Chat: Page mounted, starting initialization...');

		// Enable chat connection immediately when entering chat page
		websocketService.enableChatConnection();

		// Setup WebSocket handlers first
		setupWebSocketHandlers();

		// Initialize data loading
		(async () => {
			// Get current auth state
			const authState = get(auth);
			console.log('Chat: Current auth state:', {
				username: authState.username,
				hasToken: !!authState.token,
				isInitialized: authState.isInitialized
			});

			if (authState.token && authState.username) {
				// We have auth, fetch data (WebSocket will auto-connect via auth subscription)
				console.log('Chat: Auth available, fetching data...');
				try {
					await Promise.all([fetchUsers(), fetchGroups(), fetchCurrentUserProfile()]);
					initialLoadComplete = true;
					console.log('Chat: Initialization complete');
				} catch (error) {
					console.error('Chat: Failed to fetch data:', error);
				}
			} else {
				// No auth, validate session
				console.log('Chat: No auth found, validating session...');
				try {
					await validateSession();
				} catch (error) {
					console.error('Chat: Session validation failed:', error);
				}
			}
		})();

		// Set up periodic connection status updates
		const statusInterval = setInterval(() => {
			updateConnectionStatus();
		}, 1000);

		// Cleanup on unmount
		return () => {
			clearInterval(statusInterval);
		};
	});

	onDestroy(() => {
		console.log('Chat: Page destroying - cleaning up resources');

		// Clean up auth subscription
		if (authUnsubscribe) {
			authUnsubscribe();
			authUnsubscribe = null;
		}

		// Stop all polling and intervals
		stopMessagePolling();

		// Clear specific timeouts we created
		if (scrollCheckTimeout) {
			clearTimeout(scrollCheckTimeout);
		}
		if (scrollTimeout) {
			cancelAnimationFrame(scrollTimeout);
		}

		// Clean up WebSocket handlers
		wsUnsubscribes.forEach(unsub => {
			try {
				unsub();
			} catch (e) {
				console.warn('Error unsubscribing:', e);
			}
		});
		wsUnsubscribes = [];

		// Disable chat connection when leaving chat page
		websocketService.disableChatConnection();

		// Clear all reactive variables to prevent memory leaks
		messages = [];
		users = [];
		groups = [];
		selectedFiles = [];
	});

	let messageContainer: HTMLDivElement;
	let isScrolledToBottom = true;

	let scrollCheckTimeout: ReturnType<typeof setTimeout>;

	function checkScroll() {
		if (!messageContainer) return;

		// Throttle scroll checks to prevent excessive calculations
		if (scrollCheckTimeout) {
			clearTimeout(scrollCheckTimeout);
		}

		scrollCheckTimeout = setTimeout(() => {
			if (!messageContainer) return;
			const threshold = 20; // Pixels from bottom tolerance
			isScrolledToBottom =
				messageContainer.scrollHeight - messageContainer.scrollTop - messageContainer.clientHeight <
				threshold;
		}, 16); // ~60fps throttling
	}

	let scrollTimeout: number;

	function scrollToBottom(force = false) {
        // Only auto-scroll if user was already near the bottom, or if forced (e.g., selecting new chat)
		if (messageContainer && (isScrolledToBottom || force)) {
			// Clear any pending scroll operations
			if (scrollTimeout) {
				cancelAnimationFrame(scrollTimeout);
			}

            // Use requestAnimationFrame for smoother scrolling after DOM updates
            scrollTimeout = requestAnimationFrame(() => {
                if (messageContainer) { // Check again inside RAF
                    messageContainer.scrollTop = messageContainer.scrollHeight;
                }
            });
		}
	}

	// Reactive scroll handling
	$: if (messageContainer && messages.length) {
		// Don't auto-scroll immediately on every message update,
		// let the fetchMessages/sendMessage functions handle it after tick()
		// Check scroll position on incoming messages if needed
	}

	// Reactive block for tab switching logic
	$: {
		if (isDirectMessages) {
			if (selectedGroup) {
				// Switched FROM Groups TO Direct
				stopMessagePolling();
				selectedGroup = null;
				messages = [];
				// If a user was selected previously, maybe re-select? Usually better to clear.
				// if (previousSelectedUser) selectUser(previousSelectedUser);
			}
		} else {
			// Switched FROM Direct TO Groups
			if (selectedUser) {
				stopMessagePolling();
				selectedUser = null;
				messages = [];
                // if (previousSelectedGroup) selectGroup(previousSelectedGroup);
			}
		}
	}

	// Reactive computed values
	$: isGroupCreator =
		selectedGroup && groups.find((g) => g._id === selectedGroup)?.createdBy?.username === currentUsername;
	$: currentChatName = selectedUser
		? selectedUser
		: selectedGroup
			? groups.find((g) => g._id === selectedGroup)?.name || 'Group'
			: 'Select Chat';
	$: currentChatAvatarLetter = selectedUser
		? selectedUser[0]?.toUpperCase() || '?'
		: selectedGroup
			? '#'
			: '?';

    $: selectedGroupName = selectedGroup ? groups.find((g) => g._id === selectedGroup)?.name : null;

</script>

<PageTransition>
	<!-- Outer container -->
	<div class="flex h-screen bg-slate-50 text-sm antialiased">
	<!-- Sidebar -->
	<div class="flex w-64 flex-shrink-0 flex-col border-r border-gray-200 bg-white">
		<!-- Header -->
		<div class="flex h-16 flex-shrink-0 items-center justify-between border-b border-gray-200 px-4">
			<a href="/" class="font-mono text-xl font-semibold text-black">konekt</a>
			<div class="flex items-center gap-2">
				<!-- Simple Connection Status -->
				<div class="flex items-center gap-1 px-2 py-1">
					<div class={`h-2 w-2 rounded-full ${
						connectionStatus === 'connected' ? 'bg-green-500' :
						connectionStatus === 'connecting' ? 'bg-yellow-500' :
						'bg-red-500'
					}`}></div>
					<span class="text-xs text-gray-500 font-mono">{connectionStatus}</span>
				</div>
				<button
					on:click={handleLogout}
					class="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-red-600"
					title="Logout"
					aria-label="Logout"
				>
					<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
	                  <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
	                </svg>
				</button>
			</div>
		</div>

		<!-- Tabs -->
		<div class="flex border-b border-gray-200">
			<button
				class={`flex-1 py-2 px-3 font-mono text-xs font-medium uppercase tracking-wider transition-colors duration-150 ${isDirectMessages ? 'border-b-2 border-black text-black' : 'text-gray-500 hover:bg-gray-50 hover:text-black'}`}
				on:click={() => (isDirectMessages = true)}
			>
				Direct
			</button>
			<button
				class={`flex-1 py-2 px-3 font-mono text-xs font-medium uppercase tracking-wider transition-colors duration-150 ${!isDirectMessages ? 'border-b-2 border-black text-black' : 'text-gray-500 hover:bg-gray-50 hover:text-black'}`}
				on:click={() => (isDirectMessages = false)}
			>
				Groups
			</button>
		</div>

		<!-- Users/Groups List -->
		<div class="flex-1 overflow-y-auto p-2">
			{#if isDirectMessages}
				{#if loadingUsers}
					<div class="p-4 text-center text-xs text-gray-400">Loading users...</div>
				{:else if users.length > 0}
					{#each users as user (user)}
						<button
							class={`mb-1 flex w-full items-center gap-2 rounded p-2 text-left font-mono text-sm transition-colors duration-150 ${
								selectedUser === user
									? 'bg-black text-white'
									: 'text-gray-700 hover:bg-gray-100'
							}`}
							on:click={() => selectUser(user)}
						>
							<div class="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gray-300 text-xs font-semibold uppercase text-gray-600">
								{user[0] || '?'}
							</div>
							<span class="truncate">{user}</span>
						</button>
					{/each}
				{:else if initialLoadComplete}
					<div class="p-4 text-center text-xs text-gray-400">
						<p class="mb-2">No friends to chat with</p>
						<button
							on:click={openFriendsPanel}
							class="text-blue-500 hover:text-blue-700 underline"
						>
							Add friends
						</button>
					</div>
				{/if}
			{:else}
				{#if loadingGroups}
					<div class="p-4 text-center text-xs text-gray-400">Loading groups...</div>
				{:else if groups.length > 0}
					{#each groups as group (group._id)}
						<button
							class={`mb-1 flex w-full items-center gap-2 rounded p-2 text-left font-mono text-sm transition-colors duration-150 ${
								selectedGroup === group._id
									? 'bg-black text-white'
									: 'text-gray-700 hover:bg-gray-100'
							}`}
							on:click={() => selectGroup(group._id)}
						>
							<span class="text-gray-400">#</span>
							<span class="truncate">{group.name}</span>
						</button>
					{/each}
				{:else if initialLoadComplete}
					<p class="p-4 text-center text-xs text-gray-400">No groups yet. Create one!</p>
				{/if}
				<button
					class="mt-3 flex w-full items-center justify-center gap-1 rounded border border-gray-300 p-2 font-mono text-xs text-gray-600 transition-colors hover:border-black hover:text-black disabled:cursor-not-allowed disabled:opacity-50"
					on:click={() => (isCreatingGroup = true)}
					disabled={users.length === 0 || loadingUsers}
					title={users.length === 0 ? 'No friends available to create a group' : 'Create a new group'}
				>
					<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /> </svg>
					Create Group
				</button>
			{/if}

			<!-- Create Group Form -->
			{#if isCreatingGroup}
				<div
					class="mt-4 rounded border border-gray-200 bg-gray-50 p-3 shadow-sm"
					transition:fade={{ duration: 200 }}
				>
					  <h3 class="mb-2 text-sm font-semibold">New Group</h3>
					  <div class="space-y-2">
						  <input
							  bind:value={newGroupName}
							  placeholder="Group name"
							  class="w-full rounded border border-gray-300 p-1.5 text-sm focus:border-black focus:ring-1 focus:ring-black"
							  maxlength="50"
						  />
						  <p class="text-xs text-gray-500">Select members:</p>
						  <div class="max-h-32 overflow-y-auto rounded border border-gray-300 bg-white p-1 text-sm">
							  {#each users as user (user)}
								  <label class="flex cursor-pointer items-center rounded px-1.5 py-1 hover:bg-gray-100">
									  <input
										  type="checkbox"
										  bind:group={selectedMembers}
										  value={user}
										  class="mr-2 h-4 w-4 accent-black"
									  />
									  <span>{user}</span>
								  </label>
							  {:else}
									<p class="p-1 text-xs text-gray-400">No friends to add to group.</p>
							  {/each}
						  </div>
						  <div class="flex gap-2 pt-1">
							  <button
								  class="flex-1 rounded bg-black px-2 py-1.5 font-mono text-xs text-white transition-colors hover:bg-gray-800 disabled:opacity-50"
								  on:click={createGroup}
								  disabled={!newGroupName.trim() || selectedMembers.length === 0}
							  >
								  Create
							  </button>
							  <button
								  class="flex-1 rounded border border-gray-300 px-2 py-1.5 font-mono text-xs text-gray-700 hover:bg-gray-100"
								  on:click={() => {
									  isCreatingGroup = false;
									  newGroupName = '';
									  selectedMembers = [];
								  }}
							  >
								  Cancel
							  </button>
						  </div>
					  </div>
				  </div>
			{/if}
		</div>

		<!-- User Profile / Settings Link -->
		<div class="mt-auto flex-shrink-0 border-t border-gray-200 p-3">
			<div class="flex items-center justify-between">
				<div class="flex min-w-0 items-center gap-2">
					<div class="relative h-7 w-7 flex-shrink-0">
						<div class="flex h-full w-full items-center justify-center rounded-full bg-black text-xs font-semibold uppercase text-white">
							{currentUsername && currentUsername.length > 0 ? currentUsername[0] : '?'}
						</div>
						{#if currentUserProfile.profilePicture}
							<img src={currentUserProfile.profilePicture} alt={currentUsername} class="absolute inset-0 h-full w-full rounded-full object-cover" />
						{/if}
						<!-- Online Indicator Example (Optional) -->
						<!-- <div class="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-green-500 border border-white"></div> -->
					</div>

					<span class="truncate font-mono text-sm font-medium" title={currentUsername}>
						{currentUsername}
					</span>
				</div>
				<div class="flex items-center gap-1">
					<button
						on:click={openFriendsPanel}
						class="flex-shrink-0 rounded p-1 text-gray-500 transition-colors hover:bg-gray-100 hover:text-black {showFriendsPanel ? 'bg-gray-100 text-black' : ''}"
						title="Friends"
						aria-label="Friends"
					>
						<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
							<path stroke-linecap="round" stroke-linejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
						</svg>
					</button>
					<button
						on:click={openSettingsPanel}
						class="flex-shrink-0 rounded p-1 text-gray-500 transition-colors hover:bg-gray-100 hover:text-black {showSettingsPanel ? 'bg-gray-100 text-black' : ''}"
						title="Settings"
						aria-label="Settings"
					>
						<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
							<path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
						</svg>
					</button>
				</div>
			</div>
			<!-- Global Error/Success Messages -->
			{#if errorMessage}
				<div class="mt-2 max-w-full truncate rounded border border-red-200 bg-red-50 p-1 px-2 text-xs text-red-700" title={errorMessage}>
					{errorMessage}
				</div>
			{/if}
			{#if successMessage}
				<div class="mt-2 max-w-full truncate rounded border border-green-200 bg-green-50 p-1 px-2 text-xs text-green-700">
					{successMessage}
				</div>
			{/if}
		</div>
	</div>

	<!-- Chat Area -->
	<div class="flex flex-1 flex-col bg-gray-50 {showFriendsPanel || showSettingsPanel ? 'w-1/2' : ''}">
		{#if selectedUser || selectedGroup}
			<!-- Chat Header -->
			<div class="flex h-16 flex-shrink-0 items-center justify-between border-b border-gray-200 bg-white px-5">
				<div class="flex min-w-0 items-center gap-3">
					<div class="relative h-8 w-8 flex-shrink-0">
						 <div class="flex h-full w-full items-center justify-center rounded-full bg-black text-sm font-semibold uppercase text-white">
                           {currentChatAvatarLetter}
						</div>
						<!-- Online indicator can go here if needed -->
					</div>
					<div>
						<h2 class="truncate font-mono text-base font-semibold leading-tight">
							 {currentChatName}
						</h2>
					</div>
				</div>
                <div class="flex items-center gap-2">
                    {#if selectedGroup && isGroupCreator}
                         <button
                            on:click={deleteGroup}
                            class="ml-auto flex-shrink-0 rounded-full p-1.5 text-gray-500 transition-colors hover:bg-red-50 hover:text-red-600"
                            title={`Delete group "${selectedGroupName}"`}
                            aria-label={`Delete group "${selectedGroupName}"`}
                         >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    {/if}
                </div>
			</div>

			<!-- Message List -->
			<div bind:this={messageContainer} class="flex-1 space-y-1.5 overflow-y-auto p-4" on:scroll={checkScroll} >
				{#if loadingMessages && messages.length === 0}
					<div class="py-5 text-center text-xs text-gray-400">Loading messages...</div>
				{/if}
				{#each messages as msg (msg._id)}
					<div class={`group flex items-end gap-2 ${msg.sender === currentUsername ? 'justify-end' : 'justify-start'}`}>
						<!-- Profile picture for other users (left side) -->
						{#if msg.sender !== currentUsername}
							<div class="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gray-300 text-xs font-semibold uppercase text-gray-600 overflow-hidden">
								{#if msg.senderProfilePicture}
									<img src={msg.senderProfilePicture} alt={msg.sender} class="h-full w-full object-cover" />
								{:else}
									{msg.sender[0] || '?'}
								{/if}
							</div>
						{/if}

						<!-- Delete button for own messages -->
						{#if msg.sender === currentUsername}
							<button
								class="mb-1 mr-1 rounded p-0.5 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-100 hover:text-red-600"
								title="Delete message" aria-label="Delete message"
								on:click={() => deleteMessage(msg._id)}
							>
								 <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
							</button>
						{/if}

						<div
							class={`relative max-w-[70%] rounded-lg px-3 py-1.5 shadow-sm ${
								msg.sender === currentUsername ? 'bg-black text-white' : 'border border-gray-100 bg-white text-black'
							}`}
						>
							{#if selectedGroup && msg.sender !== currentUsername}
								<span class="mb-0.5 block text-xs font-semibold text-blue-600 opacity-90">{msg.sender}</span>
							{/if}

							{#if msg.message && msg.message.trim()}
								<p class="break-words text-sm leading-snug">{msg.message}</p>
							{/if}

							{#if msg.attachments && msg.attachments.length > 0}
								<div class="mt-2 space-y-2">
									{#each msg.attachments as attachment}
										<AttachmentDisplay {attachment} compact={true} />
									{/each}
								</div>
							{/if}

							<p class={`mt-1 text-right text-[10px] opacity-60 ${msg.sender === currentUsername ? 'text-gray-300' : 'text-gray-500'}`}>
								{formatTimestamp(msg.timestamp)}
							</p>
						</div>

						<!-- Profile picture for own messages (right side) -->
						{#if msg.sender === currentUsername}
							<div class="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gray-300 text-xs font-semibold uppercase text-gray-600 overflow-hidden">
								{#if currentUserProfile.profilePicture}
									<img src={currentUserProfile.profilePicture} alt={currentUsername} class="h-full w-full object-cover" />
								{:else}
									{currentUsername[0] || '?'}
								{/if}
							</div>
						{/if}
					</div>
				{:else}
					{#if !loadingMessages}
						<p class="pt-4 text-center text-xs text-gray-400">
							{#if selectedUser}Start a conversation with {selectedUser}.{/if}
							{#if selectedGroup}Start the conversation in #{currentChatName}.{/if}
						</p>
					{/if}
				{/each}
			</div>

			<!-- Message Input -->
			<div class="flex-shrink-0 border-t border-gray-200 bg-white p-3">
				<!-- Selected Files Preview -->
				{#if selectedFiles.length > 0}
					<div class="mb-3 space-y-2">
						<p class="text-xs font-medium text-gray-600">Attachments ({selectedFiles.length}):</p>
						<div class="flex flex-wrap gap-2">
							{#each selectedFiles as file, index}
								<div class="flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-sm">
									<span class="text-gray-600">📎</span>
									<span class="truncate max-w-32" title={file.name}>{file.name}</span>
									<button
										on:click={() => removeSelectedFile(index)}
										class="text-gray-400 hover:text-red-600"
										title="Remove file"
										aria-label="Remove file {file.name}"
									>
										<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
											<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
										</svg>
									</button>
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<div class="flex items-center gap-2">
					<!-- File Upload Button -->
					<FileUpload
						accept="image/*,.pdf,.txt,.doc,.docx,.zip,.rar,.7z,.json,.csv"
						multiple={true}
						maxSize={5 * 1024 * 1024}
						disabled={uploadingFiles}
						on:filesSelected={handleFilesSelected}
						on:error={handleFileError}
					>
						<button
							class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-lg border border-gray-300 text-gray-500 transition-colors duration-150 hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50"
							disabled={uploadingFiles}
							title="Attach files (max 5MB each)"
							aria-label="Attach files"
						>
							<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
								<path stroke-linecap="round" stroke-linejoin="round" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
							</svg>
						</button>
					</FileUpload>

					<input
						bind:value={newMessage}
						placeholder="Type a message..."
						class="flex-1 rounded-lg border border-gray-300 bg-gray-100 px-3 py-2 font-sans text-sm focus:border-black focus:bg-white focus:outline-none focus:ring-1 focus:ring-black"
						disabled={uploadingFiles}
						on:keydown={(e) => {
							if (e.key === 'Enter' && !e.shiftKey) {
								e.preventDefault();
								sendMessage();
							}
						}}
					/>
					<button
						on:click={sendMessage}
						class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-lg bg-black text-white transition-colors duration-150 hover:bg-gray-800 disabled:opacity-50"
						disabled={(!newMessage.trim() && selectedFiles.length === 0) || uploadingFiles}
						title="Send message" aria-label="Send message"
					>
						{#if uploadingFiles}
							<svg class="h-4 w-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
						{:else}
							<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
								<path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 16.571V11a1 1 0 112 0v5.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
							</svg>
						{/if}
					</button>
				</div>
			</div>
		{:else}
			<!-- Placeholder when no chat is selected -->
			 <div class="flex flex-1 flex-col items-center justify-center bg-gray-100 p-8 text-center">
                 <svg xmlns="http://www.w3.org/2000/svg" class="mb-4 h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
				<p class="font-mono text-base text-gray-500">Select a user or group</p>
                <p class="mt-1 text-sm text-gray-400">to start chatting.</p>
			</div>
		{/if}
	</div>

	<!-- Friends Panel -->
	{#if showFriendsPanel}
		<div class="w-1/2 h-full">
			<FriendsPanel onClose={closePanels} />
		</div>
	{/if}

	<!-- Settings Panel -->
	{#if showSettingsPanel}
		<div class="w-1/2 h-full">
			<SettingsPanel onClose={closePanels} />
		</div>
	{/if}

	<!-- Confirm Dialog for Message Deletion -->
	<ConfirmDialog
		bind:isOpen={showDeleteMessageDialog}
		title="Delete Message"
		message="Are you sure you want to delete this message? This action cannot be undone."
		confirmText="Delete"
		cancelText="Cancel"
		confirmVariant="danger"
		loading={deletingMessage}
		on:confirm={confirmDeleteMessage}
		on:cancel={cancelDeleteMessage}
	/>

</div>
</PageTransition>
