<script lang="ts">
	import { onMount } from 'svelte';

	interface Friend {
		_id: string;
		username: string;
		profilePicture?: string;
	}

	interface FriendRequest {
		_id: string;
		requester?: Friend;
		recipient?: Friend;
		createdAt: string;
	}

	let friends: Friend[] = [];
	let pendingRequests: FriendRequest[] = [];
	let sentRequests: FriendRequest[] = [];
	let newFriendUsername = '';
	let loading = false;
	let error = '';
	let success = '';

	onMount(() => {
		fetchFriends();
	});

	async function fetchFriends() {
		try {
			const response = await fetch('/api/friends', {
				credentials: 'include'
			});

			if (response.ok) {
				const data = await response.json();
				friends = data.friends || [];
				pendingRequests = data.pendingRequests || [];
				sentRequests = data.sentRequests || [];
			} else {
				error = 'Failed to fetch friends';
			}
		} catch (err) {
			console.error('Error fetching friends:', err);
			error = 'Failed to fetch friends';
		}
	}

	async function sendFriendRequest() {
		if (!newFriendUsername.trim()) {
			error = 'Please enter a username';
			return;
		}

		loading = true;
		error = '';
		success = '';

		try {
			const response = await fetch('/api/friends', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ username: newFriendUsername.trim() })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				newFriendUsername = '';
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to send friend request';
			}
		} catch (err) {
			console.error('Error sending friend request:', err);
			error = 'Failed to send friend request';
		} finally {
			loading = false;
		}
	}

	async function manageFriendRequest(requestId: string, action: 'accept' | 'decline') {
		try {
			const response = await fetch(`/api/friends/${requestId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				credentials: 'include',
				body: JSON.stringify({ action })
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || `Failed to ${action} friend request`;
			}
		} catch (err) {
			console.error(`Error ${action}ing friend request:`, err);
			error = `Failed to ${action} friend request`;
		}
	}

	async function removeFriend(friendshipId: string) {
		if (!confirm('Are you sure you want to remove this friend?')) {
			return;
		}

		try {
			const response = await fetch(`/api/friends/${friendshipId}`, {
				method: 'DELETE',
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok) {
				success = data.message;
				await fetchFriends(); // Refresh the lists
			} else {
				error = data.error || 'Failed to remove friend';
			}
		} catch (err) {
			console.error('Error removing friend:', err);
			error = 'Failed to remove friend';
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			sendFriendRequest();
		}
	}
</script>

<svelte:head>
	<title>Friends - Konekt</title>
</svelte:head>

<div class="friends-container">
	<h1>Friends</h1>

	{#if error}
		<div class="error">{error}</div>
	{/if}

	{#if success}
		<div class="success">{success}</div>
	{/if}

	<!-- Add Friend Section -->
	<div class="add-friend-section">
		<h2>Add Friend</h2>
		<div class="add-friend-form">
			<input
				type="text"
				placeholder="Enter username"
				bind:value={newFriendUsername}
				on:keypress={handleKeyPress}
				disabled={loading}
			/>
			<button on:click={sendFriendRequest} disabled={loading || !newFriendUsername.trim()}>
				{loading ? 'Sending...' : 'Send Request'}
			</button>
		</div>
	</div>

	<!-- Pending Friend Requests -->
	{#if pendingRequests.length > 0}
		<div class="requests-section">
			<h2>Pending Requests ({pendingRequests.length})</h2>
			<div class="requests-list">
				{#each pendingRequests as request}
					<div class="request-item">
						<div class="user-info">
							{#if request.requester?.profilePicture}
								<img src={request.requester.profilePicture} alt="Profile" class="profile-pic" />
							{:else}
								<div class="profile-pic-placeholder">
									{request.requester?.username?.charAt(0).toUpperCase()}
								</div>
							{/if}
							<span class="username">{request.requester?.username}</span>
						</div>
						<div class="request-actions">
							<button class="accept-btn" on:click={() => manageFriendRequest(request._id, 'accept')}>
								Accept
							</button>
							<button class="decline-btn" on:click={() => manageFriendRequest(request._id, 'decline')}>
								Decline
							</button>
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Sent Friend Requests -->
	{#if sentRequests.length > 0}
		<div class="requests-section">
			<h2>Sent Requests ({sentRequests.length})</h2>
			<div class="requests-list">
				{#each sentRequests as request}
					<div class="request-item">
						<div class="user-info">
							{#if request.recipient?.profilePicture}
								<img src={request.recipient.profilePicture} alt="Profile" class="profile-pic" />
							{:else}
								<div class="profile-pic-placeholder">
									{request.recipient?.username?.charAt(0).toUpperCase()}
								</div>
							{/if}
							<span class="username">{request.recipient?.username}</span>
						</div>
						<div class="request-status">
							<span class="pending-status">Pending</span>
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Friends List -->
	<div class="friends-section">
		<h2>Friends ({friends.length})</h2>
		{#if friends.length === 0}
			<p class="no-friends">No friends yet. Send some friend requests!</p>
		{:else}
			<div class="friends-list">
				{#each friends as friend}
					<div class="friend-item">
						<div class="user-info">
							{#if friend.profilePicture}
								<img src={friend.profilePicture} alt="Profile" class="profile-pic" />
							{:else}
								<div class="profile-pic-placeholder">
									{friend.username.charAt(0).toUpperCase()}
								</div>
							{/if}
							<span class="username">{friend.username}</span>
						</div>
						<div class="friend-actions">
							<button class="message-btn" on:click={() => window.location.href = `/chat?user=${friend.username}`}>
								Message
							</button>
							<button class="remove-btn" on:click={() => removeFriend(friend._id)}>
								Remove
							</button>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>
</div>

<style>
	.friends-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
	}

	h1 {
		color: #333;
		margin-bottom: 30px;
	}

	h2 {
		color: #555;
		margin-bottom: 15px;
		font-size: 1.2em;
	}

	.error {
		background: #fee;
		color: #c33;
		padding: 10px;
		border-radius: 4px;
		margin-bottom: 20px;
	}

	.success {
		background: #efe;
		color: #3c3;
		padding: 10px;
		border-radius: 4px;
		margin-bottom: 20px;
	}

	.add-friend-section {
		background: #f9f9f9;
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 30px;
	}

	.add-friend-form {
		display: flex;
		gap: 10px;
	}

	.add-friend-form input {
		flex: 1;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
	}

	.add-friend-form button {
		padding: 10px 20px;
		background: #007bff;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.add-friend-form button:disabled {
		background: #ccc;
		cursor: not-allowed;
	}

	.requests-section, .friends-section {
		margin-bottom: 30px;
	}

	.requests-list, .friends-list {
		display: flex;
		flex-direction: column;
		gap: 10px;
	}

	.request-item, .friend-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15px;
		background: white;
		border: 1px solid #eee;
		border-radius: 8px;
	}

	.user-info {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.profile-pic {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		object-fit: cover;
	}

	.profile-pic-placeholder {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		color: #666;
	}

	.username {
		font-weight: 500;
	}

	.request-actions, .friend-actions {
		display: flex;
		gap: 10px;
	}

	.accept-btn, .message-btn {
		padding: 8px 16px;
		background: #28a745;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.decline-btn, .remove-btn {
		padding: 8px 16px;
		background: #dc3545;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.pending-status {
		color: #ffc107;
		font-weight: 500;
	}

	.no-friends {
		color: #666;
		font-style: italic;
		text-align: center;
		padding: 20px;
	}

	button:hover {
		opacity: 0.9;
	}
</style>
