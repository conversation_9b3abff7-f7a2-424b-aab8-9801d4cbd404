<script lang="ts">
	import { onMount } from 'svelte';
	import { auth } from '$lib/stores/auth';
	import { goto } from '$app/navigation';
	import { performLogout } from '$lib/utils/logout';
	import ProfilePictureUpload from '$lib/components/ProfilePictureUpload.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';

	let currentUsername = '';
	let newUsername = '';
	let currentPassword = '';
	let newPassword = '';
	let confirmPassword = '';

	let previewUrl = '';
	let error = '';
	let success = '';
	let profilePictureLoading = false;

	auth.subscribe((authState) => {
		currentUsername = authState.username || '';

		// If auth is initialized and user is not logged in, redirect to login
		if (authState.isInitialized && !authState.username) {
			goto('/login');
		}
	});

	onMount(async () => {
		// Wait for auth to be initialized
		const unsubscribe = auth.subscribe(async (authState) => {
			if (authState.isInitialized) {
				if (!authState.username) {
					goto('/login');
					unsubscribe();
					return;
				}

				try {
					// Fetch user profile data
					const profileRes = await fetch('/api/user/profile', {
						credentials: 'include'
					});
					if (profileRes.ok) {
						const profile = await profileRes.json();
						previewUrl = profile.profilePicture || '';
					}
				} catch (err) {
					console.error('Error loading profile:', err);
				}
				unsubscribe();
			}
		});
	});

	function handleFileUploaded(event: CustomEvent<{ key: string; url: string }>) {
		// File was uploaded successfully, now update the user profile with the key
		updateProfilePictureKey(event.detail.key, event.detail.url);
	}

	async function updateProfilePictureKey(key: string, url: string) {
		profilePictureLoading = true;
		error = '';
		success = '';

		try {
			const formData = new FormData();
			formData.append('profilePictureKey', key);

			const res = await fetch('/api/user/profile', {
				method: 'PUT',
				credentials: 'include',
				body: formData
			});

			if (res.ok) {
				success = 'Profile picture updated successfully';
				previewUrl = url;
			} else {
				const data = await res.json();
				console.error('Profile picture key update error:', data);
				error = data.error || 'Failed to update profile picture';
			}
		} catch (err) {
			console.error('Error updating profile picture key:', err);
			error = 'Failed to update profile picture';
		} finally {
			profilePictureLoading = false;
		}
	}

	function handleRemoveProfilePicture() {
		removeProfilePicture();
	}

	function handleProfilePictureError(event: CustomEvent<{ message: string }>) {
		error = event.detail.message;
		success = '';
	}



	async function removeProfilePicture() {
		profilePictureLoading = true;
		error = '';
		success = '';

		try {
			const formData = new FormData();
			formData.append('removeProfilePicture', 'true');

			const res = await fetch('/api/user/profile', {
				method: 'PUT',
				credentials: 'include',
				body: formData
			});

			if (res.ok) {
				success = 'Profile picture removed successfully';
				previewUrl = '';
			} else {
				const data = await res.json();
				error = data.error || 'Failed to remove profile picture';
			}
		} catch (err) {
			console.error('Error removing profile picture:', err);
			error = 'Failed to remove profile picture';
		} finally {
			profilePictureLoading = false;
		}
	}

	async function updateProfile() {
		if (!newUsername) {
			error = 'Please enter a new username';
			return;
		}

		try {
			const formData = new FormData();
			formData.append('username', newUsername);

			const res = await fetch('/api/user/profile', {
				method: 'PUT',
				credentials: 'include',
				body: formData
			});

			if (res.ok) {
				const data = await res.json();
				console.log('Profile update response:', data);
				success = 'Profile updated successfully';
				error = '';

				// Get the real token from cookies
				const tokenFromCookie = document.cookie
					.split('; ')
					.find(row => row.startsWith('token='))
					?.split('=')[1];
				if (tokenFromCookie) {
					auth.setAuth(tokenFromCookie, newUsername);
				}
			} else {
				const data = await res.json();
				console.error('Profile update error:', data);
				error = data.error || 'Failed to update profile';
				success = '';
			}
		} catch (err) {
			console.error('Error updating profile:', err);
			error = 'Failed to update profile';
			success = '';
		}
	}

	async function updatePassword() {
		if (newPassword !== confirmPassword) {
			error = 'Passwords do not match';
			return;
		}

		try {
			const res = await fetch('/api/user/password', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({
					currentPassword,
					newPassword
				})
			});

			if (res.ok) {
				success = 'Password updated successfully';
				error = '';
				currentPassword = '';
				newPassword = '';
				confirmPassword = '';
			} else {
				const data = await res.json();
				error = data.error || 'Failed to update password';
				success = '';
			}
		} catch (err) {
			console.error('Error updating password:', err);
			error = 'Failed to update password';
			success = '';
		}
	}
</script>

<PageTransition>
	<div class="min-h-screen bg-slate-50 p-8">
	<div class="mx-auto max-w-2xl">
		<div class="mb-8 flex items-center justify-between">
			<h1 class="font-mono text-2xl">settings</h1>
			<div class="flex items-center gap-4">
				<button
					on:click={() => goto('/chat')}
					class="font-mono text-sm text-black hover:text-gray-600"
				>
					back to chat
				</button>
				<button
					on:click={performLogout}
					class="font-mono text-sm text-red-600 hover:text-red-800"
				>
					logout
				</button>
			</div>
		</div>

		<div class="space-y-8">
			<!-- Profile Picture Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<h2 class="mb-4 font-mono text-lg">profile picture</h2>
				<ProfilePictureUpload
					currentImageUrl={previewUrl}
					username={currentUsername}
					loading={profilePictureLoading}
					on:fileUploaded={handleFileUploaded}
					on:removeImage={handleRemoveProfilePicture}
					on:error={handleProfilePictureError}
				/>
			</div>

			<!-- Username Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<div class="space-y-4">
					<div>
						<label class="mb-2 block font-mono text-sm">Current Username</label>
						<input
							type="text"
							value={currentUsername}
							disabled
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm opacity-50"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">New Username</label>
						<input
							bind:value={newUsername}
							type="text"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
				</div>
			</div>

			<!-- Password Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<h2 class="mb-4 font-mono text-lg">change password</h2>
				<div class="space-y-4">
					<div>
						<label class="mb-2 block font-mono text-sm">Current Password</label>
						<input
							bind:value={currentPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">New Password</label>
						<input
							bind:value={newPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">Confirm New Password</label>
						<input
							bind:value={confirmPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex gap-4">
				<button
					on:click={updateProfile}
					class="flex-1 rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800"
				>
					save profile
				</button>
				<button
					on:click={updatePassword}
					class="flex-1 rounded-md border border-black bg-white px-4 py-2 font-mono text-sm transition-colors hover:bg-black hover:text-white"
				>
					update password
				</button>
			</div>

			{#if error}
				<p class="text-sm text-red-500">{error}</p>
			{/if}
			{#if success}
				<p class="text-sm text-green-500">{success}</p>
			{/if}
		</div>
	</div>
</PageTransition>
