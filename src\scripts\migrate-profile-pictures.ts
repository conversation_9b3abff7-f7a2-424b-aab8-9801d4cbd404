import { connect } from '../lib/db';
import User from '../lib/models/User';

async function migrateProfilePictures() {
	try {
		await connect();

		// Find users with old profilePicture URLs but no profilePictureKey
		const usersToMigrate = await User.find({
			profilePicture: { $exists: true, $ne: null },
			profilePictureKey: { $exists: false }
		});

		console.log(`Found ${usersToMigrate.length} users to migrate`);

		for (const user of usersToMigrate) {
			try {
				// Extract key from old URL format
				// Old format: https://your-r2-url.com/avatars/userId/filename.ext
				// New format: avatars/userId/filename.ext
				const oldUrl = user.profilePicture;
				
				if (oldUrl && oldUrl.includes('/avatars/')) {
					// Extract the key part after the domain
					const keyMatch = oldUrl.match(/\/avatars\/.*$/);
					if (keyMatch) {
						const key = keyMatch[0].substring(1); // Remove leading slash
						
						// Update user with the extracted key
						await User.updateOne(
							{ _id: user._id },
							{ 
								$set: { profilePictureKey: key },
								$unset: { profilePicture: 1 } // Remove old field
							}
						);
						
						console.log(`Migrated user ${user.username}: ${key}`);
					} else {
						console.warn(`Could not extract key from URL for user ${user.username}: ${oldUrl}`);
					}
				} else {
					console.warn(`Invalid URL format for user ${user.username}: ${oldUrl}`);
				}
			} catch (error) {
				console.error(`Error migrating user ${user.username}:`, error);
			}
		}

		console.log('Migration completed successfully');
		process.exit(0);
	} catch (error) {
		console.error('Migration failed:', error);
		process.exit(1);
	}
}

// Run migration if this file is executed directly
if (require.main === module) {
	migrateProfilePictures();
}

export default migrateProfilePictures;
