// Simple test script to check authentication
async function testAuth() {
    try {
        // Register user first
        console.log('Registering user...');
        const registerResponse = await fetch('http://localhost:5173/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'testuser2',
                password: 'test123'
            })
        });

        const registerData = await registerResponse.json();
        console.log('Register response:', registerData);

        // Test login with existing user
        console.log('Testing login...');
        const loginResponse = await fetch('http://localhost:5173/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                username: 'testuser2',
                password: 'test123'
            })
        });

        const loginData = await loginResponse.json();
        console.log('Login response:', loginData);

        if (loginResponse.ok) {
            console.log('Login successful! Token:', loginData.token.substring(0, 20) + '...');

            // Test sending a message
            console.log('Testing message send...');
            const messageResponse = await fetch('http://localhost:5173/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${loginData.token}`
                },
                body: JSON.stringify({
                    receiver: 'vape',
                    message: 'Test message from API'
                })
            });

            const messageData = await messageResponse.json();
            console.log('Message response:', messageData);
        }

    } catch (error) {
        console.error('Error:', error);
    }
}

testAuth();
